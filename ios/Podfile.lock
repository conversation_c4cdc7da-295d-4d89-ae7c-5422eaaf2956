PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - ReachabilitySwift (5.2.4)
  - speech_to_text (0.0.1):
    - Flutter
    - FlutterMacOS
    - Try
  - Try (2.1.1)

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - speech_to_text (from `.symlinks/plugins/speech_to_text/darwin`)

SPEC REPOS:
  trunk:
    - ReachabilitySwift
    - Try

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  speech_to_text:
    :path: ".symlinks/plugins/speech_to_text/darwin"

SPEC CHECKSUMS:
  connectivity_plus: 481668c94744c30c53b8895afb39159d1e619bdf
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  speech_to_text: 9dc43a5df3cbc2813f8c7cc9bd0fbf94268ed7ac
  Try: 5ef669ae832617b3cee58cb2c6f99fb767a4ff96

PODFILE CHECKSUM: af06d5cb56a25d4863b67ca4b174558404836c92

COCOAPODS: 1.16.2
