import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class Scene {
  final String id;
  final String name;
  final Map<String, dynamic> entities;
  final String icon;
  final Map<String, dynamic> metadata;

  Scene({
    required this.id,
    required this.name,
    required this.entities,
    required this.icon,
    required this.metadata,
  });

  factory Scene.fromJson(Map<String, dynamic> json) {
    return Scene(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      entities: json['entities'] ?? {},
      icon: json['icon'] ?? 'mdi:lightbulb-group',
      metadata: json['metadata'] ?? {},
    );
  }
}

class EntityInfo {
  final String entityId;
  final String friendlyName;
  final String state;
  final Map<String, dynamic> attributes;
  final String domain;

  EntityInfo({
    required this.entityId,
    required this.friendlyName,
    required this.state,
    required this.attributes,
    required this.domain,
  });

  factory EntityInfo.fromJson(Map<String, dynamic> json) {
    return EntityInfo(
      entityId: json['entity_id'] ?? '',
      friendlyName:
          json['attributes']?['friendly_name'] ?? json['entity_id'] ?? '',
      state: json['state'] ?? '',
      attributes: json['attributes'] ?? {},
      domain: json['entity_id']?.split('.').first ?? '',
    );
  }
}

class ApiService {
  static const String baseUrl = 'http://192.168.6.166:8123/api';
  static const String sceneConfigUrl = '$baseUrl/config/scene/config';
  static const String statesUrl = '$baseUrl/states';
  static const String scenesUrl = '$baseUrl/states';
  static const String authToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI5ZjVlM2QyNjQ4ZjQ0NDAwOWE0NWExNjJlMDAwZmRjZSIsImlhdCI6MTczODgxODgwNSwiZXhwIjoyMDU0MTc4ODA1fQ.vOEYd3rL0poOHWUOkf0_QTwxpkzDTtubCzpvlb9zWcY';

  static Map<String, String> get headers => {
    'Authorization': 'Bearer $authToken',
    'Content-Type': 'application/json',
  };

  static Future<List<EntityInfo>> getAllEntities() async {
    try {
      final response = await http.get(Uri.parse(statesUrl), headers: headers);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data
            .map((entity) => EntityInfo.fromJson(entity))
            .where(
              (entity) =>
                  entity.domain == 'light' ||
                  entity.domain == 'fan' ||
                  entity.domain == 'switch' ||
                  entity.domain == 'input_boolean',
            )
            .toList();
      }
      return [];
    } catch (e) {
      log('Error fetching entities: $e');
      return [];
    }
  }

  static Future<bool> createScene({
    required String sceneId,
    required String name,
    required Map<String, dynamic> entities,
    String icon = 'mdi:lightbulb-group',
  }) async {
    try {
      final url = '$sceneConfigUrl/$sceneId';

      final body = {
        "name": name,
        "entities": entities,
        "icon": icon,
        "metadata": {},
      };

      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        // Reload scenes after creation
        await reloadScenes();
        return true;
      }
      return false;
    } catch (e) {
      log('Error creating scene: $e');
      return false;
    }
  }

  static Future<bool> reloadScenes() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/services/scene/reload'),
        headers: headers,
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error reloading scenes: $e');
      return false;
    }
  }

static Future<List<Scene>> getAllScenes() async {
  try {
    final response = await http.get(Uri.parse(statesUrl), headers: headers);
    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data
          .where((entity) => entity['entity_id'].toString().startsWith('scene.'))
          .map((scene) {
            final attributes = scene['attributes'] ?? {};
            return Scene(
              id: scene['entity_id'].toString().replaceFirst('scene.', ''),
              name: attributes['friendly_name'] ?? 'Unnamed Scene',
              entities: attributes['entities'] ?? {},
              icon: attributes['icon'] ?? 'mdi:lightbulb-group',
              metadata: {},
            );
          })
          .toList()
          .reversed 
          .toList();
    }
    return [];
  } catch (e) {
    log('Error fetching scenes: $e');
    return [];
  }
}
  static Future<bool> callService({
    required String domain,
    required String service,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/services/$domain/$service'),
        headers: headers,
        body: json.encode(data),
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error calling service: $e');
      return false;
    }
  }
}

class SceneListPage extends StatefulWidget {
  const SceneListPage({super.key});

  @override
  SceneListPageState createState() => SceneListPageState();
}

class SceneListPageState extends State<SceneListPage> {
  List<Scene> scenes = [];
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    loadScenes();
  }

  Future<void> loadScenes() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });
    try {
      final loadedScenes = await ApiService.getAllScenes();
      setState(() {
        scenes = loadedScenes;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load scenes: ${e.toString()}';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scenes'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: loadScenes),
        ],
      ),
      body:
          isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Colors.blue),
              )
              : RefreshIndicator(
                onRefresh: loadScenes,
                color: Colors.blue,
                child: Column(
                  children: [
                    if (errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(16),
                        color: Colors.red[100],
                        child: Row(
                          children: [
                            const Icon(Icons.error, color: Colors.red),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                errorMessage!,
                                style: const TextStyle(color: Colors.red),
                              ),
                            ),
                          ],
                        ),
                      ),
                    Expanded(
                      child:
                          scenes.isEmpty
                              ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.palette,
                                      size: 64,
                                      color: Colors.blue,
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'No scenes found',
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Pull to refresh or create a new scene',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.blue.withOpacity(0.7),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                              : ListView.builder(
                                itemCount: scenes.length,
                                itemBuilder: (context, index) {
                                  final scene = scenes[index];
                                  return Card(
                                    margin: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    child: ListTile(
                                      leading: CircleAvatar(
                                        backgroundColor: Colors.blue,
                                        child: const Icon(
                                          Icons.palette,
                                          color: Colors.white,
                                        ),
                                      ),
                                      title: Text(
                                        scene.name,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      subtitle: Text(
                                        '${scene.entities.length} entities',
                                        style: TextStyle(
                                          color: Colors.blue.withOpacity(0.7),
                                        ),
                                      ),
                                      trailing: const Icon(
                                        Icons.arrow_forward_ios,
                                        color: Colors.blue,
                                      ),
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) => SceneDetailPage(
                                                  scene: scene,
                                                ),
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
                    ),
                  ],
                ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CreateScenePage()),
          );
          if (result == true) {
            loadScenes();
          }
        },
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}

class CreateScenePage extends StatefulWidget {
  const CreateScenePage({super.key});

  @override
  CreateScenePageState createState() => CreateScenePageState();
}

class CreateScenePageState extends State<CreateScenePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _idController = TextEditingController();
  final _iconController = TextEditingController(text: 'mdi:lightbulb-group');

  List<EntityInfo> _entities = [];
  List<EntityInfo> _selectedEntities = [];
  Map<String, Map<String, dynamic>> _entityStates = {};
  bool _isLoadingEntities = true;
  bool _isCreating = false;
  bool _showDeviceDialog = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadEntities();
  }

  Future<void> _loadEntities() async {
    setState(() {
      _isLoadingEntities = true;
      _errorMessage = null;
    });
    try {
      final entities = await ApiService.getAllEntities();
      setState(() {
        _entities = entities;
        _isLoadingEntities = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load devices: ${e.toString()}';
        _isLoadingEntities = false;
      });
    }
  }

  void _onEntitySelected(EntityInfo entity) {
    setState(() {
      if (!_selectedEntities.any((e) => e.entityId == entity.entityId)) {
        _selectedEntities.add(entity);
        _entityStates[entity.entityId] = _buildDefaultEntityState(entity);
      }
      _showDeviceDialog = false;
    });
  }

  Map<String, dynamic> _buildDefaultEntityState(EntityInfo entity) {
    final state = <String, dynamic>{
      'state': entity.state,
      'friendly_name': entity.friendlyName,
    };

    // Add domain-specific attributes
    switch (entity.domain) {
      case 'light':
        state['brightness'] = entity.attributes['brightness'] ?? 255;
        if (entity.attributes['rgb_color'] != null) {
          state['rgb_color'] = entity.attributes['rgb_color'];
        }
        if (entity.attributes['supported_color_modes'] != null) {
          state['supported_color_modes'] =
              entity.attributes['supported_color_modes'];
        }
        break;
      case 'fan':
        state['percentage'] = entity.attributes['percentage'] ?? 50;
        if (entity.attributes['preset_modes'] != null) {
          state['preset_mode'] = entity.attributes['preset_mode'] ?? 'low';
          state['preset_modes'] = entity.attributes['preset_modes'];
        }
        break;
      case 'switch':
      case 'input_boolean':
        // Switches typically only have state
        break;
    }

    return state;
  }

  Widget _buildEntityControls(EntityInfo entity) {
    final controls = <Widget>[];
    final entityState = _entityStates[entity.entityId] ?? {};

    // State control (common for all entities)
    controls.add(
      DropdownButtonFormField<String>(
        value: entityState['state'] ?? entity.state,
        decoration: const InputDecoration(
          labelText: 'State',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.power_settings_new, color: Colors.blue),
        ),
        items:
            ['on', 'off']
                .map(
                  (String value) => DropdownMenuItem<String>(
                    value: value,
                    child: Text(value.toUpperCase()),
                  ),
                )
                .toList(),
        onChanged: (String? newValue) {
          setState(() {
            _entityStates[entity.entityId]!['state'] = newValue!;
          });
        },
      ),
    );

    // Domain-specific controls
    switch (entity.domain) {
      case 'light':
        controls.add(const SizedBox(height: 16));

        // Brightness control
        controls.add(
          Slider(
            value: (entityState['brightness']?.toDouble() ?? 255).clamp(0, 255),
            min: 0,
            max: 255,
            divisions: 255,
            label: 'Brightness: ${entityState['brightness'] ?? 255}',
            onChanged: (value) {
              setState(() {
                _entityStates[entity.entityId]!['brightness'] = value.toInt();
              });
            },
          ),
        );

        // RGB Color control (if supported)
        if (entity.attributes['supported_color_modes']?.contains('rgb') ==
            true) {
          controls.add(const SizedBox(height: 16));
          controls.add(
            const Text(
              'RGB Color',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          );
          controls.add(const SizedBox(height: 8));

          final currentRgb = entityState['rgb_color'] ?? [255, 255, 255];
          controls.add(
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    initialValue: currentRgb[0].toString(),
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Red (0-255)',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final red = int.tryParse(value);
                      if (red != null && red >= 0 && red <= 255) {
                        setState(() {
                          _entityStates[entity.entityId]!['rgb_color'] = [
                            red,
                            currentRgb[1],
                            currentRgb[2],
                          ];
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    initialValue: currentRgb[1].toString(),
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Green (0-255)',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final green = int.tryParse(value);
                      if (green != null && green >= 0 && green <= 255) {
                        setState(() {
                          _entityStates[entity.entityId]!['rgb_color'] = [
                            currentRgb[0],
                            green,
                            currentRgb[2],
                          ];
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    initialValue: currentRgb[2].toString(),
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Blue (0-255)',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final blue = int.tryParse(value);
                      if (blue != null && blue >= 0 && blue <= 255) {
                        setState(() {
                          _entityStates[entity.entityId]!['rgb_color'] = [
                            currentRgb[0],
                            currentRgb[1],
                            blue,
                          ];
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          );
        }
        break;

      case 'fan':
        if (entity.attributes['preset_modes'] != null) {
          controls.add(const SizedBox(height: 16));
          controls.add(
            DropdownButtonFormField<String>(
              value:
                  entityState['preset_mode'] ??
                  entity.attributes['preset_mode'],
              decoration: const InputDecoration(
                labelText: 'Preset Mode',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.air, color: Colors.blue),
              ),
              items:
                  (entity.attributes['preset_modes'] as List<dynamic>)
                      .map(
                        (mode) => DropdownMenuItem<String>(
                          value: mode.toString(),
                          child: Text(mode.toString().toUpperCase()),
                        ),
                      )
                      .toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _entityStates[entity.entityId]!['preset_mode'] = newValue!;
                });
              },
            ),
          );
        }

        controls.add(const SizedBox(height: 16));
        controls.add(
          Slider(
            value: (entityState['percentage']?.toDouble() ?? 50).clamp(0, 100),
            min: 0,
            max: 100,
            divisions: 20,
            label: 'Speed: ${entityState['percentage'] ?? 50}%',
            onChanged: (value) {
              setState(() {
                _entityStates[entity.entityId]!['percentage'] = value.toInt();
              });
            },
          ),
        );
        break;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: controls,
    );
  }

  Widget _buildDeviceCard(EntityInfo entity) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      entity.friendlyName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      entity.entityId,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () {
                    setState(() {
                      _selectedEntities.removeWhere(
                        (e) => e.entityId == entity.entityId,
                      );
                      _entityStates.remove(entity.entityId);
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildEntityControls(entity),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _idController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  Future<void> _createScene() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedEntities.isEmpty) {
      setState(() {
        _errorMessage = 'Please add at least one device';
      });
      return;
    }

    setState(() {
      _isCreating = true;
      _errorMessage = null;
    });

    try {
      // Build the entities map for the scene
      final entitiesMap = <String, dynamic>{};
      for (final entity in _selectedEntities) {
        entitiesMap[entity.entityId] = _entityStates[entity.entityId];
      }

      final sceneId =
          _idController.text.isNotEmpty
              ? _idController.text
              : 'scene_${DateTime.now().millisecondsSinceEpoch}';

      final success = await ApiService.createScene(
        sceneId: sceneId,
        name: _nameController.text,
        entities: entitiesMap,
        icon: _iconController.text,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Scene created successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to create scene'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Scene'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoadingEntities
              ? const Center(
                child: CircularProgressIndicator(color: Colors.blue),
              )
              : Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    if (_errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(16),
                        color: Colors.red[100],
                        child: Row(
                          children: [
                            const Icon(Icons.error, color: Colors.red),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: const TextStyle(color: Colors.red),
                              ),
                            ),
                          ],
                        ),
                      ),
                    TextFormField(
                      controller: _idController,
                      decoration: const InputDecoration(
                        labelText: 'Scene ID (optional)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.code, color: Colors.blue),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Scene Name',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.palette, color: Colors.blue),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a scene name';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _iconController,
                      decoration: const InputDecoration(
                        labelText: 'Scene Icon (Material Design Icon code)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.emoji_objects,
                          color: Colors.blue,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _showDeviceDialog = true;
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.add, color: Colors.white),
                          SizedBox(width: 8),
                          Text(
                            'Add Device',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_selectedEntities.isNotEmpty)
                      Text(
                        'Devices in Scene (${_selectedEntities.length})',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ..._selectedEntities.map(
                      (entity) => _buildDeviceCard(entity),
                    ),
                    const SizedBox(height: 32),
                    ElevatedButton(
                      onPressed: _isCreating ? null : _createScene,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child:
                          _isCreating
                              ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Creating...',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ],
                              )
                              : const Text(
                                'Create Scene',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                ),
                              ),
                    ),
                  ],
                ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadEntities,
        backgroundColor: Colors.blue,
        mini: true,
        child: const Icon(Icons.refresh, color: Colors.white),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      bottomSheet:
          _showDeviceDialog
              ? DeviceDialog(
                devices: _entities,
                onDismiss: () {
                  setState(() {
                    _showDeviceDialog = false;
                  });
                },
                onDeviceSelected: _onEntitySelected,
              )
              : null,
    );
  }
}

class DeviceDialog extends StatelessWidget {
  final List<EntityInfo> devices;
  final VoidCallback onDismiss;
  final Function(EntityInfo) onDeviceSelected;

  const DeviceDialog({
    super.key,
    required this.devices,
    required this.onDismiss,
    required this.onDeviceSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Select Device',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              IconButton(icon: const Icon(Icons.close), onPressed: onDismiss),
            ],
          ),
          Expanded(
            child: ListView.builder(
              itemCount: devices.length,
              itemBuilder: (context, index) {
                final device = devices[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: ListTile(
                    title: Text(device.friendlyName),
                    subtitle: Text(device.entityId),
                    trailing: Text(
                      device.domain.toUpperCase(),
                      style: TextStyle(
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    onTap: () => onDeviceSelected(device),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class SceneDetailPage extends StatelessWidget {
  final Scene scene;

  const SceneDetailPage({super.key, required this.scene});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(scene.name),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.info, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        'Scene Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow('ID', scene.id),
                  _buildInfoRow('Name', scene.name),
                  _buildInfoRow('Icon', scene.icon),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.device_hub, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        'Entities (${scene.entities.length})',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ...scene.entities.entries.map((entity) {
                    return _buildEntityCard(entity.key, entity.value);
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 16))),
        ],
      ),
    );
  }

  Widget _buildEntityCard(String entityId, dynamic entityData) {
    final domain = entityId.split('.').first;
    final state = entityData['state']?.toString() ?? 'unknown';
    final friendlyName = entityData['friendly_name']?.toString() ?? entityId;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            friendlyName,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.blue,
            ),
          ),
          Text(
            entityId,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildEntityProperty('Type', domain),
              _buildEntityProperty('State', state),
              if (entityData['brightness'] != null)
                _buildEntityProperty(
                  'Brightness',
                  entityData['brightness'].toString(),
                ),
              if (entityData['percentage'] != null)
                _buildEntityProperty('Speed', '${entityData['percentage']}%'),
              if (entityData['preset_mode'] != null)
                _buildEntityProperty(
                  'Preset',
                  entityData['preset_mode'].toString(),
                ),
            ],
          ),
          if (entityData['rgb_color'] != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('Color Preview: '),
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(
                      entityData['rgb_color'][0] ?? 0,
                      entityData['rgb_color'][1] ?? 0,
                      entityData['rgb_color'][2] ?? 0,
                      1.0,
                    ),
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEntityProperty(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(value, style: const TextStyle(fontSize: 14)),
      ],
    );
  }
}
