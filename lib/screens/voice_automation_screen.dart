import 'package:flutter/material.dart';
import '../services/speech_service.dart';
import '../services/openai_service.dart';
import 'automation.dart';

class VoiceAutomationScreen extends StatefulWidget {
  const VoiceAutomationScreen({super.key});

  @override
  State<VoiceAutomationScreen> createState() => _VoiceAutomationScreenState();
}

class _VoiceAutomationScreenState extends State<VoiceAutomationScreen>
    with TickerProviderStateMixin {
  bool _isListening = false;
  bool _isProcessing = false;
  String _currentText = '';
  String _statusMessage = 'Tap the microphone to start speaking';
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  final List<AutomationTemplate> _templates = [
    AutomationTemplate(
      title: 'Good Morning Rou..',
      description: 'Activate morning scene at a specific time',
      example: 'Turn on good morning scene at 7 AM',
      icon: Icons.wb_sunny,
      color: Colors.orange,
    ),
    AutomationTemplate(
      title: 'Good Night Routine',
      description: 'Activate night scene at bedtime',
      example: 'Turn on good night scene at 10 PM',
      icon: Icons.bedtime,
      color: Colors.indigo,
    ),
    AutomationTemplate(
      title: 'Motion Detection',
      description: 'Turn on lights when motion is detected',
      example: 'Turn on living room lights when motion detected',
      icon: Icons.motion_photos_on,
      color: Colors.green,
    ),
    AutomationTemplate(
      title: 'Time-based Scene',
      description: 'Activate any scene at a specific time',
      example: 'Turn on scene 1234 at 6:30 PM',
      icon: Icons.schedule,
      color: Colors.blue,
    ),
    AutomationTemplate(
      title: 'Sunset Automation',
      description: 'Trigger actions at sunset',
      example: 'Turn on outdoor lights at sunset',
      icon: Icons.wb_twilight,
      color: Colors.deepOrange,
    ),
    AutomationTemplate(
      title: 'Device Control',
      description: 'Control specific devices based on conditions',
      example: 'Turn off all lights when everyone leaves',
      icon: Icons.power_settings_new,
      color: Colors.red,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
    _initializeSpeech();
  }

  void _initializeAnimation() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  Future<void> _initializeSpeech() async {
    final initialized = await SpeechService.initialize();
    if (!initialized && mounted) {
      setState(() {
        _statusMessage = 'Speech recognition not available';
      });
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _startListening() async {
    if (_isListening || _isProcessing) return;

    setState(() {
      _isListening = true;
      _currentText = '';
      _statusMessage = 'Listening... Speak your automation request';
    });

    _pulseController.repeat(reverse: true);

    try {
      final result = await SpeechService.startListening(
        timeout: const Duration(seconds: 10),
        onPartialResult: (text) {
          if (mounted) {
            setState(() {
              _currentText = text;
            });
          }
        },
      );

      _pulseController.stop();

      if (mounted) {
        setState(() {
          _isListening = false;
          _currentText = result ?? '';
        });

        if (result != null && result.isNotEmpty) {
          await _processVoiceInput(result);
        } else {
          setState(() {
            _statusMessage = 'No speech detected. Try again.';
          });
        }
      }
    } catch (e) {
      _pulseController.stop();
      if (mounted) {
        setState(() {
          _isListening = false;
          _statusMessage = 'Error: $e';
        });
      }
    }
  }

  Future<void> _processVoiceInput(String input) async {
    setState(() {
      _isProcessing = true;
      _statusMessage = 'Processing your request...';
    });

    try {
      final automationData = await OpenAIService.convertVoiceToAutomation(
        input,
      );

      if (automationData != null && mounted) {
        await _createAutomationFromData(automationData);
      } else if (mounted) {
        setState(() {
          _statusMessage =
              'Could not understand the request. Please try again.';
          _isProcessing = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _statusMessage = 'Error processing request: $e';
          _isProcessing = false;
        });
      }
    }
  }

  Future<void> _createAutomationFromData(Map<String, dynamic> data) async {
    try {
      final automationId =
          'automation_${DateTime.now().millisecondsSinceEpoch}';

      final automation = Automation(
        id: automationId,
        alias: data['alias'] ?? 'Voice Created Automation',
        description: data['description'],
        triggers:
            (data['triggers'] as List?)
                ?.map((t) => AutomationTrigger.fromJson(t))
                .toList() ??
            [],
        conditions:
            (data['conditions'] as List?)
                ?.map((c) => AutomationCondition.fromJson(c))
                .toList(),
        actions:
            (data['actions'] as List?)
                ?.map((a) => AutomationAction.fromJson(a))
                .toList() ??
            [],
        mode: data['mode'] ?? 'single',
        initial_state: true,
        entityId: 'automation.$automationId',
      );

      final success = await AutomationApiService.createAutomation(automation);

      if (mounted) {
        setState(() {
          _isProcessing = false;
        });

        if (success) {
          _showSuccessDialog(automation);
        } else {
          setState(() {
            _statusMessage = 'Failed to create automation. Please try again.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isProcessing = false;
          _statusMessage = 'Error creating automation: $e';
        });
      }
    }
  }

  void _showSuccessDialog(Automation automation) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Automation Created!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Name: ${automation.alias}'),
                if (automation.description != null) ...[
                  const SizedBox(height: 8),
                  Text('Description: ${automation.description}'),
                ],
                const SizedBox(height: 8),
                Text('Triggers: ${automation.triggers.length}'),
                Text('Actions: ${automation.actions.length}'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) =>
                              AutomationDetailPage(automation: automation),
                    ),
                  );
                },
                child: const Text('View Details'),
              ),
            ],
          ),
    );
  }

  void _useTemplate(AutomationTemplate template) {
    setState(() {
      _currentText = template.example;
      _statusMessage = 'Template selected. Tap microphone to modify or create.';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Voice Automation Creator'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Voice Input Section
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.blue.shade50, Colors.blue.shade100],
              ),
            ),
            child: Column(
              children: [
                // Microphone Button
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _isListening ? _pulseAnimation.value : 1.0,
                      child: GestureDetector(
                        onTap: _isProcessing ? null : _startListening,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color:
                                _isListening
                                    ? Colors.red
                                    : _isProcessing
                                    ? Colors.orange
                                    : Colors.blue,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.2),
                                blurRadius: 10,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Icon(
                            _isListening
                                ? Icons.mic
                                : _isProcessing
                                ? Icons.hourglass_empty
                                : Icons.mic_none,
                            size: 40,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
                Text(
                  _statusMessage,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (_currentText.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Text(
                      _currentText,
                      style: const TextStyle(
                        fontSize: 16,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Templates Section
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Quick Templates',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Tap a template to use as starting point, then speak to customize',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 1.2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                      itemCount: _templates.length,
                      itemBuilder: (context, index) {
                        final template = _templates[index];
                        return Card(
                          elevation: 4,
                          child: InkWell(
                            onTap: () => _useTemplate(template),
                            borderRadius: BorderRadius.circular(12),
                            child: Padding(
                              padding: const EdgeInsets.all(12),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    template.icon,
                                    size: 32,
                                    color: template.color,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    template.title,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    template.description,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class AutomationTemplate {
  final String title;
  final String description;
  final String example;
  final IconData icon;
  final Color color;

  AutomationTemplate({
    required this.title,
    required this.description,
    required this.example,
    required this.icon,
    required this.color,
  });
}
