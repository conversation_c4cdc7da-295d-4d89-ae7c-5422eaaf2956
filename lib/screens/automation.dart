import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

// ================== Scene Model ==================
class Scene {
  final String id;
  final String name;
  final String entityId;
  final String state;

  Scene({
    required this.id,
    required this.name,
    required this.entityId,
    required this.state,
  });

  factory Scene.fromJson(Map<String, dynamic> json) {
    return Scene(
      id: json['entity_id'].toString().replaceFirst('scene.', ''),
      name: json['attributes']['friendly_name'] ?? json['entity_id'],
      entityId: json['entity_id'],
      state: json['state'] ?? 'unknown',
    );
  }
}

// ================== Automation Models ==================
class Automation {
  final String id;
  final String alias;
  final String? description;
  final List<AutomationTrigger> triggers;
  final List<AutomationCondition>? conditions;
  final List<AutomationAction> actions;
  final String mode;
  final bool? initial_state;
  final String? state;
  final String entityId;

  Automation({
    required this.id,
    required this.alias,
    this.description,
    required this.triggers,
    this.conditions,
    required this.actions,
    this.mode = 'single',
    this.initial_state,
    this.state,
    required this.entityId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'alias': alias,
      if (description != null) 'description': description,
      'trigger': triggers.map((t) => t.toJson()).toList(),
      if (conditions != null && conditions!.isNotEmpty)
        'condition': conditions!.map((c) => c.toJson()).toList(),
      'action': actions.map((a) => a.toJson()).toList(),
      'mode': mode,
      if (initial_state != null) 'initial_state': initial_state,
    };
  }

  factory Automation.fromJson(Map<String, dynamic> json) {
    return Automation(
      id: json['id'] ?? '',
      alias: json['alias'] ?? '',
      description: json['description'],
      triggers:
          (json['trigger'] as List?)
              ?.map((t) => AutomationTrigger.fromJson(t))
              .toList() ??
          [],
      conditions:
          (json['condition'] as List?)
              ?.map((c) => AutomationCondition.fromJson(c))
              .toList(),
      actions:
          (json['action'] as List?)
              ?.map((a) => AutomationAction.fromJson(a))
              .toList() ??
          [],
      mode: json['mode'] ?? 'single',
      initial_state: json['initial_state'],
      state: null,
      entityId: json['entity_id'] ?? '',
    );
  }

  factory Automation.fromStateEntity(Map<String, dynamic> json) {
    final attributes = json['attributes'] ?? {};
    return Automation(
      id: json['entity_id'].toString().replaceFirst('automation.', ''),
      alias: attributes['friendly_name'] ?? json['entity_id'],
      description: attributes['description'],
      triggers: [],
      conditions: null,
      actions: [],
      mode: attributes['mode'] ?? 'single',
      initial_state: null,
      state: json['state'],
      entityId: json['entity_id'],
    );
  }
}

class AutomationTrigger {
  final String platform;
  final Map<String, dynamic> data;

  AutomationTrigger({required this.platform, required this.data});

  Map<String, dynamic> toJson() {
    return {'platform': platform, ...data};
  }

  factory AutomationTrigger.fromJson(Map<String, dynamic> json) {
    final data = Map<String, dynamic>.from(json);
    data.remove('platform');
    return AutomationTrigger(platform: json['platform'] ?? 'state', data: data);
  }
}

class AutomationCondition {
  final String condition;
  final Map<String, dynamic> data;

  AutomationCondition({required this.condition, required this.data});

  Map<String, dynamic> toJson() {
    return {'condition': condition, ...data};
  }

  factory AutomationCondition.fromJson(Map<String, dynamic> json) {
    final data = Map<String, dynamic>.from(json);
    data.remove('condition');
    return AutomationCondition(
      condition: json['condition'] ?? 'state',
      data: data,
    );
  }
}

class AutomationAction {
  final String? service;
  final String? scene;
  final Map<String, dynamic>? target;
  final Map<String, dynamic>? data;

  AutomationAction({this.service, this.scene, this.target, this.data});

  Map<String, dynamic> toJson() {
    if (scene != null) {
      return {'scene': scene};
    }
    return {
      'service': service,
      if (target != null) 'target': target,
      if (data != null) 'data': data,
    };
  }

  factory AutomationAction.fromJson(Map<String, dynamic> json) {
    if (json.containsKey('scene')) {
      return AutomationAction(scene: json['scene']);
    }
    return AutomationAction(
      service: json['service'],
      target:
          json['target'] != null
              ? Map<String, dynamic>.from(json['target'])
              : null,
      data:
          json['data'] != null ? Map<String, dynamic>.from(json['data']) : null,
    );
  }
}

// ================== API Service ==================
class AutomationApiService {
  static const String baseUrl = 'http://192.168.6.166:8123/api';
  static const String automationConfigUrl = '$baseUrl/config/automation/config';
  static const String statesUrl = '$baseUrl/states';
  static const String servicesUrl = '$baseUrl/services';
  static const String authToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI5ZjVlM2QyNjQ4ZjQ0NDAwOWE0NWExNjJlMDAwZmRjZSIsImlhdCI6MTczODgxODgwNSwiZXhwIjoyMDU0MTc4ODA1fQ.vOEYd3rL0poOHWUOkf0_QTwxpkzDTtubCzpvlb9zWcY';

  static Map<String, String> get headers => {
    'Authorization': 'Bearer $authToken',
    'Content-Type': 'application/json',
  };

  static Future<bool> createAutomation(Automation automation) async {
    try {
      log('Creating automation: ${automation.toJson()}');
      final response = await http.post(
        Uri.parse('$automationConfigUrl/${automation.id}'),
        headers: headers,
        body: json.encode(automation.toJson()),
      );

      log(
        'Create automation response: ${response.statusCode} ${response.body}',
      );

      if (response.statusCode == 200) {
        await reloadAutomations();
        return true;
      }
      return false;
    } catch (e) {
      log('Error creating automation: $e');
      return false;
    }
  }

  static Future<bool> reloadAutomations() async {
    try {
      final response = await http.post(
        Uri.parse('$servicesUrl/automation/reload'),
        headers: headers,
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error reloading automations: $e');
      return false;
    }
  }

  static Future<bool> toggleAutomation(String entityId, bool enable) async {
    try {
      final service = enable ? 'turn_on' : 'turn_off';
      final response = await http.post(
        Uri.parse('$servicesUrl/automation/$service'),
        headers: headers,
        body: json.encode({'entity_id': entityId}),
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error toggling automation: $e');
      return false;
    }
  }

  static Future<bool> triggerAutomation(String entityId) async {
    try {
      final response = await http.post(
        Uri.parse('$servicesUrl/automation/trigger'),
        headers: headers,
        body: json.encode({'entity_id': entityId}),
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error triggering automation: $e');
      return false;
    }
  }

  static Future<bool> activateScene(String sceneId) async {
    try {
      final response = await http.post(
        Uri.parse('$servicesUrl/scene/turn_on'),
        headers: headers,
        body: json.encode({'entity_id': sceneId}),
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error activating scene: $e');
      return false;
    }
  }

  static Future<List<Automation>> getAllAutomations() async {
    try {
      final response = await http.get(Uri.parse(statesUrl), headers: headers);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data
            .where(
              (entity) =>
                  entity['entity_id'].toString().startsWith('automation.'),
            )
            .map((automation) => Automation.fromStateEntity(automation))
            .toList();
      }
      return [];
    } catch (e) {
      log('Error fetching automations: $e');
      return [];
    }
  }

  static Future<Automation?> getAutomationDetails(String automationId) async {
    try {
      final response = await http.get(
        Uri.parse('$automationConfigUrl/$automationId'),
        headers: headers,
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Automation.fromJson(data);
      }
      return null;
    } catch (e) {
      log('Error fetching automation details: $e');
      return null;
    }
  }

  static Future<List<Scene>> getAllScenes() async {
    try {
      final response = await http.get(Uri.parse(statesUrl), headers: headers);
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data
            .where(
              (entity) => entity['entity_id'].toString().startsWith('scene.'),
            )
            .map((scene) => Scene.fromJson(scene))
            .toList();
      }
      return [];
    } catch (e) {
      log('Error fetching scenes: $e');
      return [];
    }
  }
}

// ================== UI Components ==================
class AutomationListPage extends StatefulWidget {
  const AutomationListPage({super.key});

  @override
  State<AutomationListPage> createState() => _AutomationListPageState();
}

class _AutomationListPageState extends State<AutomationListPage> {
  List<Automation> _automations = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAutomations();
  }

  Future<void> _loadAutomations() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final automations = await AutomationApiService.getAllAutomations();
      setState(() {
        _automations = automations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load automations: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleAutomation(Automation automation, bool newState) async {
    final success = await AutomationApiService.toggleAutomation(
      automation.entityId,
      newState,
    );

    if (success) {
      setState(() {
        final index = _automations.indexWhere((a) => a.id == automation.id);
        if (index != -1) {
          _automations[index] = Automation(
            id: automation.id,
            alias: automation.alias,
            description: automation.description,
            triggers: automation.triggers,
            conditions: automation.conditions,
            actions: automation.actions,
            mode: automation.mode,
            initial_state: automation.initial_state,
            state: newState ? 'on' : 'off',
            entityId: automation.entityId,
          );
        }
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to toggle automation')),
      );
    }
  }

  Future<void> _triggerAutomation(Automation automation) async {
    final success = await AutomationApiService.triggerAutomation(
      automation.entityId,
    );

    if (success) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Triggered ${automation.alias}')));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to trigger automation')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Automations'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAutomations,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Colors.blue),
              )
              : RefreshIndicator(
                onRefresh: _loadAutomations,
                color: Colors.blue,
                child: Column(
                  children: [
                    if (_errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(16),
                        color: Colors.red[100],
                        child: Row(
                          children: [
                            const Icon(Icons.error, color: Colors.red),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: const TextStyle(color: Colors.red),
                              ),
                            ),
                          ],
                        ),
                      ),
                    Expanded(
                      child:
                          _automations.isEmpty
                              ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.settings,
                                      size: 64,
                                      color: Colors.blue,
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'No automations found',
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Pull to refresh or create a new automation',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.blue.withValues(
                                          alpha: 0.7,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                              : ListView.builder(
                                itemCount: _automations.length,
                                itemBuilder: (context, index) {
                                  final automation = _automations[index];
                                  final isActive = automation.state == 'on';

                                  return Card(
                                    margin: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    child: ListTile(
                                      title: Text(
                                        automation.alias,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            automation.description ??
                                                'No description',
                                            style: TextStyle(
                                              color: Colors.blue.withValues(
                                                alpha: 0.7,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            'State: ${automation.state?.toUpperCase() ?? 'UNKNOWN'}',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color:
                                                  isActive
                                                      ? Colors.green
                                                      : Colors.grey,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      trailing: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          IconButton(
                                            icon: const Icon(Icons.play_arrow),
                                            color: Colors.blue,
                                            onPressed: () {
                                              _triggerAutomation(automation);
                                            },
                                          ),
                                          PopupMenuButton<String>(
                                            icon: const Icon(Icons.more_vert),
                                            onSelected: (value) {
                                              if (value == 'toggle') {
                                                _toggleAutomation(
                                                  automation,
                                                  !isActive,
                                                );
                                              }
                                            },
                                            itemBuilder:
                                                (BuildContext context) => [
                                                  PopupMenuItem<String>(
                                                    value: 'toggle',
                                                    child: Row(
                                                      children: [
                                                        Text(
                                                          isActive
                                                              ? 'Disable'
                                                              : 'Enable',
                                                        ),
                                                        const SizedBox(
                                                          width: 8,
                                                        ),
                                                        Switch(
                                                          value: isActive,
                                                          onChanged: (value) {
                                                            Navigator.pop(
                                                              context,
                                                            );
                                                            _toggleAutomation(
                                                              automation,
                                                              value,
                                                            );
                                                          },
                                                          activeColor:
                                                              Colors.green,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                          ),
                                        ],
                                      ),
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) =>
                                                    AutomationDetailPage(
                                                      automation: automation,
                                                    ),
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
                    ),
                  ],
                ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateAutomationPage(),
            ),
          );
          if (result == true) {
            _loadAutomations();
          }
        },
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}

// [Rest of the code remains the same - CreateAutomationPage, AutomationDetailPage, and MyApp classes]

class CreateAutomationPage extends StatefulWidget {
  const CreateAutomationPage({super.key});

  @override
  State<CreateAutomationPage> createState() => _CreateAutomationPageState();
}

class _CreateAutomationPageState extends State<CreateAutomationPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descController = TextEditingController();

  final List<AutomationTrigger> _triggers = [];
  final List<AutomationCondition> _conditions = [];
  final List<AutomationAction> _actions = [];
  TimeOfDay? _selectedTime;
  String? _selectedTriggerType;
  String? _selectedSceneId;
  List<Scene> _availableScenes = [];
  bool _isSubmitting = false;
  bool _scenesLoaded = false;
  bool _isLoadingScenes = false;
  String? _loadError;
  bool _initialState = true;

  @override
  void initState() {
    super.initState();
    _loadScenes();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descController.dispose();
    super.dispose();
  }

  Future<void> _loadScenes() async {
    setState(() {
      _isLoadingScenes = true;
      _loadError = null;
      _scenesLoaded = false;
    });

    try {
      final scenes = await AutomationApiService.getAllScenes();

      if (mounted) {
        setState(() {
          _availableScenes = scenes;
          _scenesLoaded = true;
          _isLoadingScenes = false;
          _loadError = null;
        });

        if (scenes.isEmpty) {
          setState(() {
            _loadError = 'No scenes found. Create scenes first.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _scenesLoaded = true;
          _isLoadingScenes = false;
          _availableScenes = [];
          _loadError = 'Failed to load scenes: $e';
        });
      }
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (time != null) {
      setState(() => _selectedTime = time);
    }
  }

  void _addTimeTrigger() {
    if (_selectedTime != null) {
      setState(() {
        _triggers.add(
          AutomationTrigger(
            platform: 'time',
            data: {
              'at':
                  '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}',
            },
          ),
        );
        _selectedTime = null;
        _selectedTriggerType = null;
      });
    }
  }

  void _addSunsetTrigger() {
    setState(() {
      _triggers.add(
        AutomationTrigger(
          platform: 'sun',
          data: {'event': 'sunset', 'offset': '-01:00:00'},
        ),
      );
    });
  }

  void _addStateTrigger() {
    setState(() {
      _triggers.add(
        AutomationTrigger(
          platform: 'state',
          data: {'entity_id': 'binary_sensor.motion_sensor', 'to': 'on'},
        ),
      );
    });
  }

  void _addSceneAction() {
    if (_selectedSceneId != null) {
      setState(() {
        _actions.add(AutomationAction(scene: _selectedSceneId!));
        _selectedSceneId = null;
      });
    }
  }

  void _addServiceAction() {
    setState(() {
      _actions.add(
        AutomationAction(
          service: 'light.turn_on',
          target: {'entity_id': 'light.living_room'},
        ),
      );
    });
  }

  void _addNotificationAction() {
    setState(() {
      _actions.add(
        AutomationAction(
          service: 'notify.notify',
          data: {'message': 'Automation triggered'},
        ),
      );
    });
  }

  void _removeTrigger(int index) {
    setState(() {
      _triggers.removeAt(index);
    });
  }

  void _removeAction(int index) {
    setState(() {
      _actions.removeAt(index);
    });
  }

  String _getSceneName(String entityId) {
    try {
      final scene = _availableScenes.firstWhere((s) => s.entityId == entityId);
      return scene.name;
    } catch (e) {
      return entityId.replaceFirst('scene.', '');
    }
  }

  Future<void> _submitAutomation() async {
    if (!_formKey.currentState!.validate()) return;

    if (_triggers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one trigger')),
      );
      return;
    }

    if (_actions.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one action')),
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final automationId =
          'automation_${DateTime.now().millisecondsSinceEpoch}';
      final automation = Automation(
        id: automationId,
        alias: _nameController.text,
        description: _descController.text.isEmpty ? null : _descController.text,
        triggers: _triggers,
        conditions: _conditions.isNotEmpty ? _conditions : null,
        actions: _actions,
        mode: 'single',
        initial_state: _initialState,
        entityId: 'automation.$automationId',
      );

      final success = await AutomationApiService.createAutomation(automation);

      if (success && mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Automation created successfully')),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to create automation')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Automation')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Automation Name',
                  border: OutlineInputBorder(),
                ),
                validator:
                    (value) =>
                        value?.isEmpty ?? true ? 'Name is required' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              SwitchListTile(
                title: const Text('Initial State'),
                subtitle: const Text('Enable/disable automation by default'),
                value: _initialState,
                onChanged: (value) => setState(() => _initialState = value),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16.0),
                child: Text(
                  'Triggers',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
              Wrap(
                spacing: 8,
                children: [
                  ElevatedButton(
                    onPressed: _addSunsetTrigger,
                    child: const Text('Add Sunset Trigger'),
                  ),
                  ElevatedButton(
                    onPressed: _addStateTrigger,
                    child: const Text('Add State Trigger'),
                  ),
                  ElevatedButton(
                    onPressed:
                        () => setState(() => _selectedTriggerType = 'time'),
                    child: const Text('Add Time Trigger'),
                  ),
                ],
              ),
              if (_selectedTriggerType == 'time') ...[
                const SizedBox(height: 16),
                ListTile(
                  title: Text(
                    _selectedTime?.format(context) ?? 'No time selected',
                  ),
                  subtitle: const Text('Tap to select time'),
                  leading: const Icon(Icons.access_time),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () => _selectTime(context),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                const SizedBox(height: 8),
                if (_selectedTime != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _addTimeTrigger,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Time Trigger'),
                    ),
                  ),
              ],
              if (_triggers.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'Added Triggers:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                ...List.generate(_triggers.length, (index) {
                  final trigger = _triggers[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Icon(
                        trigger.platform == 'time'
                            ? Icons.access_time
                            : trigger.platform == 'sun'
                            ? Icons.wb_sunny
                            : Icons.swap_horiz,
                        color: Colors.blue,
                      ),
                      title: Text(
                        trigger.platform == 'time'
                            ? 'At ${trigger.data['at']}'
                            : trigger.platform == 'sun'
                            ? 'Sunset with offset ${trigger.data['offset']}'
                            : '${trigger.data['entity_id']} → ${trigger.data['to']}',
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _removeTrigger(index),
                      ),
                    ),
                  );
                }),
              ],
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16.0),
                child: Text(
                  'Actions',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
              Wrap(
                spacing: 8,
                children: [
                  ElevatedButton(
                    onPressed: _addServiceAction,
                    child: const Text('Add Light Action'),
                  ),
                  ElevatedButton(
                    onPressed: _addNotificationAction,
                    child: const Text('Add Notification'),
                  ),
                ],
              ),
              if (_availableScenes.isNotEmpty) ...[
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedSceneId,
                  hint: const Text('Select a scene to activate'),
                  items:
                      _availableScenes.map((scene) {
                        return DropdownMenuItem<String>(
                          value: scene.entityId,
                          child: Text(scene.name),
                        );
                      }).toList(),
                  onChanged: (sceneId) {
                    setState(() => _selectedSceneId = sceneId);
                  },
                  decoration: const InputDecoration(
                    labelText: 'Scene Action',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 8),
                if (_selectedSceneId != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _addSceneAction,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Scene Action'),
                    ),
                  ),
              ],
              if (_actions.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'Added Actions:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                ...List.generate(_actions.length, (index) {
                  final action = _actions[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Icon(
                        action.scene != null ? Icons.palette : Icons.settings,
                        color: Colors.green,
                      ),
                      title: Text(
                        action.scene != null
                            ? 'Activate: ${_getSceneName(action.scene!)}'
                            : 'Service: ${action.service}',
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _removeAction(index),
                      ),
                    ),
                  );
                }),
              ],
              Padding(
                padding: const EdgeInsets.only(top: 32.0, bottom: 16.0),
                child: SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isSubmitting ? null : _submitAutomation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child:
                        _isSubmitting
                            ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                ),
                                SizedBox(width: 12),
                                Text('Creating...'),
                              ],
                            )
                            : const Text(
                              'Create Automation',
                              style: TextStyle(fontSize: 16),
                            ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class AutomationDetailPage extends StatefulWidget {
  final Automation automation;

  const AutomationDetailPage({super.key, required this.automation});

  @override
  State<AutomationDetailPage> createState() => _AutomationDetailPageState();
}

class _AutomationDetailPageState extends State<AutomationDetailPage> {
  Automation? _detailedAutomation;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAutomationDetails();
  }

  Future<void> _loadAutomationDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // First try to get the detailed automation config
      final detailedAutomation =
          await AutomationApiService.getAutomationDetails(widget.automation.id);

      if (detailedAutomation != null) {
        // Merge the state information from the original automation with the detailed config
        final mergedAutomation = Automation(
          id: detailedAutomation.id,
          alias: detailedAutomation.alias,
          description: detailedAutomation.description,
          triggers: detailedAutomation.triggers,
          conditions: detailedAutomation.conditions,
          actions: detailedAutomation.actions,
          mode: detailedAutomation.mode,
          initial_state: detailedAutomation.initial_state,
          state: widget.automation.state, // Keep the current state from list
          entityId: widget.automation.entityId,
        );

        setState(() {
          _detailedAutomation = mergedAutomation;
          _isLoading = false;
        });
      } else {
        // If detailed config fails, fall back to showing what we have from the list
        setState(() {
          _detailedAutomation = widget.automation;
          _errorMessage =
              'Could not load detailed configuration. Showing basic info only.';
          _isLoading = false;
        });
      }
    } catch (e) {
      // If there's an error, fall back to showing what we have from the list
      setState(() {
        _detailedAutomation = widget.automation;
        _errorMessage =
            'Error loading detailed config: $e. Showing basic info only.';
        _isLoading = false;
      });
    }
  }

  Future<void> _activateScene(String sceneId) async {
    try {
      final success = await AutomationApiService.activateScene(sceneId);
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Scene activated: ${sceneId.replaceFirst('scene.', '')}',
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to activate scene')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error activating scene: $e')));
      }
    }
  }

  String _formatTriggerDescription(AutomationTrigger trigger) {
    switch (trigger.platform) {
      case 'time':
        return 'Time: ${trigger.data['at'] ?? 'Unknown time'}';
      case 'sun':
        final event = trigger.data['event'] ?? 'Unknown';
        final offset = trigger.data['offset'];
        return 'Sun: $event${offset != null ? ' (offset: $offset)' : ''}';
      case 'state':
        final entityId = trigger.data['entity_id'] ?? 'Unknown entity';
        final from = trigger.data['from'];
        final to = trigger.data['to'];
        String stateChange = '';
        if (from != null && to != null) {
          stateChange = '$from → $to';
        } else if (to != null) {
          stateChange = '→ $to';
        } else if (from != null) {
          stateChange = '$from →';
        }
        return 'State: $entityId $stateChange';
      case 'device':
        final deviceId = trigger.data['device_id'] ?? 'Unknown device';
        final domain = trigger.data['domain'] ?? '';
        return 'Device: $deviceId${domain.isNotEmpty ? ' ($domain)' : ''}';
      case 'event':
        final eventType = trigger.data['event_type'] ?? 'Unknown event';
        return 'Event: $eventType';
      default:
        return '${trigger.platform}: ${trigger.data.toString()}';
    }
  }

  String _formatActionDescription(AutomationAction action) {
    if (action.scene != null) {
      return 'Activate scene: ${action.scene!.replaceFirst('scene.', '')}';
    }
    if (action.service != null) {
      String text = 'Service: ${action.service}';
      if (action.target != null && action.target!['entity_id'] != null) {
        text += ' → ${action.target!['entity_id']}';
      }
      if (action.data != null && action.data!.isNotEmpty) {
        final dataStr = action.data!.entries
            .map((e) => '${e.key}: ${e.value}')
            .join(', ');
        text += ' ($dataStr)';
      }
      return text;
    }
    return 'Unknown action';
  }

  String _formatConditionDescription(AutomationCondition condition) {
    switch (condition.condition) {
      case 'state':
        final entityId = condition.data['entity_id'] ?? 'Unknown entity';
        final state = condition.data['state'];
        return 'State: $entityId = $state';
      case 'time':
        final after = condition.data['after'];
        final before = condition.data['before'];
        if (after != null && before != null) {
          return 'Time: between $after and $before';
        } else if (after != null) {
          return 'Time: after $after';
        } else if (before != null) {
          return 'Time: before $before';
        }
        return 'Time condition';
      case 'sun':
        final after = condition.data['after_offset'];
        final before = condition.data['before_offset'];
        return 'Sun: ${after ?? before ?? 'condition'}';
      case 'numeric_state':
        final entityId = condition.data['entity_id'] ?? 'Unknown entity';
        final above = condition.data['above'];
        final below = condition.data['below'];
        String range = '';
        if (above != null && below != null) {
          range = '$above < value < $below';
        } else if (above != null) {
          range = 'value > $above';
        } else if (below != null) {
          range = 'value < $below';
        }
        return 'Numeric: $entityId $range';
      default:
        return '${condition.condition}: ${condition.data.toString()}';
    }
  }

  IconData _getTriggerIcon(String platform) {
    switch (platform) {
      case 'time':
        return Icons.schedule;
      case 'sun':
        return Icons.wb_sunny;
      case 'state':
        return Icons.toggle_on;
      case 'device':
        return Icons.devices;
      case 'event':
        return Icons.event;
      case 'mqtt':
        return Icons.wifi;
      case 'webhook':
        return Icons.webhook;
      default:
        return Icons.play_circle_outline;
    }
  }

  IconData _getActionIcon(AutomationAction action) {
    if (action.scene != null) {
      return Icons.movie;
    }
    if (action.service != null) {
      if (action.service!.contains('light')) {
        return Icons.lightbulb;
      } else if (action.service!.contains('switch')) {
        return Icons.power_settings_new;
      } else if (action.service!.contains('climate')) {
        return Icons.thermostat;
      } else if (action.service!.contains('media_player')) {
        return Icons.play_arrow;
      } else if (action.service!.contains('notify')) {
        return Icons.notifications;
      }
    }
    return Icons.flash_on;
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: Colors.blue[700], size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final automation = _detailedAutomation ?? widget.automation;
    final isActive = automation.state == 'on';

    return Scaffold(
      backgroundColor: Colors.black38,
      appBar: AppBar(
        title: Text(
          automation.alias,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(isActive ? Icons.toggle_on : Icons.toggle_off),
            color: Colors.white,
            onPressed: () async {
              final success = await AutomationApiService.toggleAutomation(
                automation.entityId,
                !isActive,
              );
              if (mounted) {
                if (success) {
                  setState(() {
                    // Create a new automation with updated state
                    _detailedAutomation = Automation(
                      id: automation.id,
                      alias: automation.alias,
                      description: automation.description,
                      triggers: automation.triggers,
                      conditions: automation.conditions,
                      actions: automation.actions,
                      mode: automation.mode,
                      initial_state: automation.initial_state,
                      state: !isActive ? 'on' : 'off',
                      entityId: automation.entityId,
                    );
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Automation ${!isActive ? 'enabled' : 'disabled'}',
                      ),
                    ),
                  );
                }
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.play_arrow),
            color: Colors.white,
            onPressed: () async {
              final success = await AutomationApiService.triggerAutomation(
                automation.entityId,
              );
              if (mounted) {
                if (success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Triggered ${automation.alias}')),
                  );
                }
              }
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Colors.blue),
              )
              : RefreshIndicator(
                onRefresh: _loadAutomationDetails,
                color: Colors.blue,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Error message if any
                      if (_errorMessage != null)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.orange[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange[300]!),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.warning, color: Colors.orange[700]),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _errorMessage!,
                                  style: TextStyle(color: Colors.orange[700]),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Status Card
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: LinearGradient(
                              colors:
                                  isActive
                                      ? [
                                        const Color.fromARGB(
                                          255,
                                          137,
                                          190,
                                          139,
                                        ),
                                        const Color.fromARGB(
                                          255,
                                          137,
                                          190,
                                          139,
                                        ),
                                      ]
                                      : [Colors.grey[400]!, Colors.grey[600]!],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    isActive
                                        ? Icons.check_circle
                                        : Icons.pause_circle,
                                    color: Colors.white,
                                    size: 28,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Status',
                                          style: TextStyle(
                                            color: Colors.white.withValues(
                                              alpha: 0.9,
                                            ),
                                            fontSize: 14,
                                          ),
                                        ),
                                        Text(
                                          isActive ? 'ACTIVE' : 'INACTIVE',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(
                                        alpha: 0.2,
                                      ),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Text(
                                      automation.mode.toUpperCase(),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Description Card
                      _buildInfoCard(
                        title: 'Description',
                        icon: Icons.description,
                        child: Text(
                          automation.description ?? 'No description provided',
                          style: const TextStyle(
                            fontSize: 16,
                            height: 1.5,
                            color: Colors.white,
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Triggers Card
                      _buildInfoCard(
                        title: 'Triggers',
                        icon: Icons.play_circle_outline,
                        child:
                            automation.triggers.isEmpty
                                ? const Text(
                                  'No triggers configured',
                                  style: TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: Colors.grey,
                                  ),
                                )
                                : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children:
                                      automation.triggers
                                          .asMap()
                                          .entries
                                          .map(
                                            (entry) => Container(
                                              margin: EdgeInsets.only(
                                                bottom:
                                                    entry.key <
                                                            automation
                                                                    .triggers
                                                                    .length -
                                                                1
                                                        ? 12
                                                        : 0,
                                              ),
                                              padding: const EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                color: Colors.blue[50],
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                  color: Colors.blue[200]!,
                                                ),
                                              ),
                                              child: Row(
                                                children: [
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.all(6),
                                                    decoration: BoxDecoration(
                                                      color: Colors.blue[100],
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            6,
                                                          ),
                                                    ),
                                                    child: Icon(
                                                      _getTriggerIcon(
                                                        entry.value.platform,
                                                      ),
                                                      size: 16,
                                                      color: Colors.blue[700],
                                                    ),
                                                  ),
                                                  const SizedBox(width: 12),
                                                  Expanded(
                                                    child: Text(
                                                      _formatTriggerDescription(
                                                        entry.value,
                                                      ),
                                                      style: const TextStyle(
                                                        fontSize: 14,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          )
                                          .toList(),
                                ),
                      ),

                      const SizedBox(height: 16),

                      // Conditions Card (if any)
                      if (automation.conditions?.isNotEmpty == true) ...[
                        _buildInfoCard(
                          title: 'Conditions',
                          icon: Icons.rule,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children:
                                automation.conditions!
                                    .asMap()
                                    .entries
                                    .map(
                                      (entry) => Container(
                                        margin: EdgeInsets.only(
                                          bottom:
                                              entry.key <
                                                      automation
                                                              .conditions!
                                                              .length -
                                                          1
                                                  ? 12
                                                  : 0,
                                        ),
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Colors.orange[50],
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          border: Border.all(
                                            color: Colors.orange[200]!,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(6),
                                              decoration: BoxDecoration(
                                                color: Colors.orange[100],
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                              ),
                                              child: Icon(
                                                Icons.rule,
                                                size: 16,
                                                color: Colors.orange[700],
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            Expanded(
                                              child: Text(
                                                _formatConditionDescription(
                                                  entry.value,
                                                ),
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    )
                                    .toList(),
                        ),
                      ),
                        const SizedBox(height: 16),
                      ],

                      // Actions Card
                      _buildInfoCard(
                        title: 'Actions',
                        icon: Icons.flash_on,
                        child:
                            automation.actions.isEmpty
                                ? const Text(
                                  'No actions configured',
                                  style: TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: Colors.grey,
                                  ),
                                )
                                : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children:
                                      automation.actions
                                          .asMap()
                                          .entries
                                          .map(
                                            (entry) => Container(
                                              margin: EdgeInsets.only(
                                                bottom:
                                                    entry.key <
                                                            automation
                                                                    .actions
                                                                    .length -
                                                                1
                                                        ? 12
                                                        : 0,
                                              ),
                                              padding: const EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                color: Colors.green[50],
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                  color: Colors.green[200]!,
                                                ),
                                              ),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Container(
                                                        padding:
                                                            const EdgeInsets.all(
                                                              6,
                                                            ),
                                                        decoration: BoxDecoration(
                                                          color:
                                                              Colors.green[100],
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                6,
                                                              ),
                                                        ),
                                                        child: Icon(
                                                          _getActionIcon(
                                                            entry.value,
                                                          ),
                                                          size: 16,
                                                          color:
                                                              Colors.green[700],
                                                        ),
                                                      ),
                                                      const SizedBox(width: 12),
                                                      Expanded(
                                                        child: Text(
                                                          _formatActionDescription(
                                                            entry.value,
                                                          ),
                                                          style:
                                                              const TextStyle(
                                                                fontSize: 14,
                                                              ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  if (entry.value.scene != null)
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                            top: 8,
                                                          ),
                                                      child: SizedBox(
                                                        width: double.infinity,
                                                        child: ElevatedButton.icon(
                                                          onPressed:
                                                              () =>
                                                                  _activateScene(
                                                                    entry
                                                                        .value
                                                                        .scene!,
                                                                  ),
                                                          icon: const Icon(
                                                            Icons.play_arrow,
                                                            size: 16,
                                                          ),
                                                          label: const Text(
                                                            'Test Action',
                                                          ),
                                                          style: ElevatedButton.styleFrom(
                                                            backgroundColor:
                                                                Colors
                                                                    .green[600],
                                                            foregroundColor:
                                                                Colors.white,
                                                            padding:
                                                                const EdgeInsets.symmetric(
                                                                  vertical: 8,
                                                                ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          )
                                          .toList(),
                                ),
                      ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
    );
  }
}

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Home Assistant Automations',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
        inputDecorationTheme: const InputDecorationTheme(
          border: OutlineInputBorder(),
        ),
      ),
      home: const AutomationListPage(),
    );
  }
}
