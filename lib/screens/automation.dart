import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

// ================== Scene Model ==================
class Scene {
  final String id;
  final String name;
  final String entityId;
  final String state;

  Scene({
    required this.id,
    required this.name,
    required this.entityId,
    required this.state,
  });

  factory Scene.fromJson(Map<String, dynamic> json) {
    return Scene(
      id: json['entity_id'].toString().replaceFirst('scene.', ''),
      name: json['attributes']['friendly_name'] ?? json['entity_id'],
      entityId: json['entity_id'],
      state: json['state'] ?? 'unknown',
    );
  }
}

// ================== Automation Models ==================
class Automation {
  final String id;
  final String alias;
  final String? description;
  final List<AutomationTrigger> triggers;
  final List<AutomationCondition>? conditions;
  final List<AutomationAction> actions;
  final String mode;
  final bool? initial_state;
  final String? state;
  final String entityId;

  Automation({
    required this.id,
    required this.alias,
    this.description,
    required this.triggers,
    this.conditions,
    required this.actions,
    this.mode = 'single',
    this.initial_state,
    this.state,
    required this.entityId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'alias': alias,
      if (description != null) 'description': description,
      'trigger': triggers.map((t) => t.toJson()).toList(),
      if (conditions != null && conditions!.isNotEmpty)
        'condition': conditions!.map((c) => c.toJson()).toList(),
      'action': actions.map((a) => a.toJson()).toList(),
      'mode': mode,
      if (initial_state != null) 'initial_state': initial_state,
    };
  }

  factory Automation.fromJson(Map<String, dynamic> json) {
    return Automation(
      id: json['id'] ?? '',
      alias: json['alias'] ?? '',
      description: json['description'],
      triggers:
          (json['trigger'] as List?)
              ?.map((t) => AutomationTrigger.fromJson(t))
              .toList() ??
          [],
      conditions:
          (json['condition'] as List?)
              ?.map((c) => AutomationCondition.fromJson(c))
              .toList(),
      actions:
          (json['action'] as List?)
              ?.map((a) => AutomationAction.fromJson(a))
              .toList() ??
          [],
      mode: json['mode'] ?? 'single',
      initial_state: json['initial_state'],
      state: null,
      entityId: json['entity_id'] ?? '',
    );
  }

  factory Automation.fromStateEntity(Map<String, dynamic> json) {
    final attributes = json['attributes'] ?? {};
    return Automation(
      id: json['entity_id'].toString().replaceFirst('automation.', ''),
      alias: attributes['friendly_name'] ?? json['entity_id'],
      description: attributes['description'],
      triggers: [],
      conditions: null,
      actions: [],
      mode: attributes['mode'] ?? 'single',
      initial_state: null,
      state: json['state'],
      entityId: json['entity_id'],
    );
  }
}

class AutomationTrigger {
  final String platform;
  final Map<String, dynamic> data;

  AutomationTrigger({required this.platform, required this.data});

  Map<String, dynamic> toJson() {
    return {'platform': platform, ...data};
  }

  factory AutomationTrigger.fromJson(Map<String, dynamic> json) {
    final data = Map<String, dynamic>.from(json);
    data.remove('platform');
    return AutomationTrigger(platform: json['platform'] ?? 'state', data: data);
  }
}

class AutomationCondition {
  final String condition;
  final Map<String, dynamic> data;

  AutomationCondition({required this.condition, required this.data});

  Map<String, dynamic> toJson() {
    return {'condition': condition, ...data};
  }

  factory AutomationCondition.fromJson(Map<String, dynamic> json) {
    final data = Map<String, dynamic>.from(json);
    data.remove('condition');
    return AutomationCondition(
      condition: json['condition'] ?? 'state',
      data: data,
    );
  }
}

class AutomationAction {
  final String? service;
  final String? scene;
  final Map<String, dynamic>? target;
  final Map<String, dynamic>? data;

  AutomationAction({this.service, this.scene, this.target, this.data});

  Map<String, dynamic> toJson() {
    if (scene != null) {
      return {'scene': scene};
    }
    return {
      'service': service,
      if (target != null) 'target': target,
      if (data != null) 'data': data,
    };
  }

  factory AutomationAction.fromJson(Map<String, dynamic> json) {
    if (json.containsKey('scene')) {
      return AutomationAction(scene: json['scene']);
    }
    return AutomationAction(
      service: json['service'],
      target:
          json['target'] != null
              ? Map<String, dynamic>.from(json['target'])
              : null,
      data:
          json['data'] != null ? Map<String, dynamic>.from(json['data']) : null,
    );
  }
}

// ================== API Service ==================
class AutomationApiService {
  static const String baseUrl = 'http://192.168.6.166:8123/api';
  static const String automationConfigUrl = '$baseUrl/config/automation/config';
  static const String statesUrl = '$baseUrl/states';
  static const String servicesUrl = '$baseUrl/services';
  static const String authToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI5ZjVlM2QyNjQ4ZjQ0NDAwOWE0NWExNjJlMDAwZmRjZSIsImlhdCI6MTczODgxODgwNSwiZXhwIjoyMDU0MTc4ODA1fQ.vOEYd3rL0poOHWUOkf0_QTwxpkzDTtubCzpvlb9zWcY';

  static Map<String, String> get headers => {
    'Authorization': 'Bearer $authToken',
    'Content-Type': 'application/json',
  };

  static Future<bool> createAutomation(Automation automation) async {
    try {
      log('Creating automation: ${automation.toJson()}');
      final response = await http.post(
        Uri.parse('$automationConfigUrl/${automation.id}'),
        headers: headers,
        body: json.encode(automation.toJson()),
      );

      log(
        'Create automation response: ${response.statusCode} ${response.body}',
      );

      if (response.statusCode == 200) {
        await reloadAutomations();
        return true;
      }
      return false;
    } catch (e) {
      log('Error creating automation: $e');
      return false;
    }
  }

  static Future<bool> reloadAutomations() async {
    try {
      final response = await http.post(
        Uri.parse('$servicesUrl/automation/reload'),
        headers: headers,
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error reloading automations: $e');
      return false;
    }
  }

  static Future<bool> toggleAutomation(String entityId, bool enable) async {
    try {
      final service = enable ? 'turn_on' : 'turn_off';
      final response = await http.post(
        Uri.parse('$servicesUrl/automation/$service'),
        headers: headers,
        body: json.encode({'entity_id': entityId}),
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error toggling automation: $e');
      return false;
    }
  }

  static Future<bool> triggerAutomation(String entityId) async {
    try {
      final response = await http.post(
        Uri.parse('$servicesUrl/automation/trigger'),
        headers: headers,
        body: json.encode({'entity_id': entityId}),
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error triggering automation: $e');
      return false;
    }
  }

  static Future<bool> activateScene(String sceneId) async {
    try {
      final response = await http.post(
        Uri.parse('$servicesUrl/scene/turn_on'),
        headers: headers,
        body: json.encode({'entity_id': sceneId}),
      );
      return response.statusCode == 200;
    } catch (e) {
      log('Error activating scene: $e');
      return false;
    }
  }

  static Future<List<Automation>> getAllAutomations() async {
    try {
      final response = await http.get(
        Uri.parse(automationConfigUrl),
        headers: headers,
      );
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((a) => Automation.fromJson(a)).toList();
      }
      return [];
    } catch (e) {
      log('Error fetching automations: $e');
      return [];
    }
  }

  static Future<Automation?> getAutomationDetails(String automationId) async {
    try {
      final response = await http.get(
        Uri.parse('$automationConfigUrl/$automationId'),
        headers: headers,
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Automation.fromJson(data);
      }
      return null;
    } catch (e) {
      log('Error fetching automation details: $e');
      return null;
    }
  }

  static Future<List<Scene>> getAllScenes() async {
    try {
      final response = await http.get(Uri.parse(statesUrl), headers: headers);
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data
            .where(
              (entity) => entity['entity_id'].toString().startsWith('scene.'),
            )
            .map((scene) => Scene.fromJson(scene))
            .toList();
      }
      return [];
    } catch (e) {
      log('Error fetching scenes: $e');
      return [];
    }
  }
}

// ================== UI Components ==================
class AutomationListPage extends StatefulWidget {
  const AutomationListPage({super.key});

  @override
  State<AutomationListPage> createState() => _AutomationListPageState();
}

class _AutomationListPageState extends State<AutomationListPage> {
  List<Automation> _automations = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAutomations();
  }

  Future<void> _loadAutomations() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final automations = await AutomationApiService.getAllAutomations();
      setState(() {
        _automations = automations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load automations: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleAutomation(Automation automation, bool newState) async {
    final success = await AutomationApiService.toggleAutomation(
      automation.entityId,
      newState,
    );

    if (success) {
      setState(() {
        final index = _automations.indexWhere((a) => a.id == automation.id);
        if (index != -1) {
          _automations[index] = Automation(
            id: automation.id,
            alias: automation.alias,
            description: automation.description,
            triggers: automation.triggers,
            conditions: automation.conditions,
            actions: automation.actions,
            mode: automation.mode,
            initial_state: automation.initial_state,
            state: newState ? 'on' : 'off',
            entityId: automation.entityId,
          );
        }
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to toggle automation')),
      );
    }
  }

  Future<void> _triggerAutomation(Automation automation) async {
    final success = await AutomationApiService.triggerAutomation(
      automation.entityId,
    );

    if (success) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Triggered ${automation.alias}')));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to trigger automation')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Automations'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAutomations,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Colors.blue),
              )
              : RefreshIndicator(
                onRefresh: _loadAutomations,
                color: Colors.blue,
                child: Column(
                  children: [
                    if (_errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(16),
                        color: Colors.red[100],
                        child: Row(
                          children: [
                            const Icon(Icons.error, color: Colors.red),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: const TextStyle(color: Colors.red),
                              ),
                            ),
                          ],
                        ),
                      ),
                    Expanded(
                      child:
                          _automations.isEmpty
                              ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.settings,
                                      size: 64,
                                      color: Colors.blue,
                                    ),
                                    const SizedBox(height: 16),
                                    const Text(
                                      'No automations found',
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Pull to refresh or create a new automation',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.blue.withValues(
                                          alpha: 0.7,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                              : ListView.builder(
                                itemCount: _automations.length,
                                itemBuilder: (context, index) {
                                  final automation = _automations[index];
                                  final isActive = automation.state == 'on';

                                  return Card(
                                    margin: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    child: ListTile(
                                      title: Text(
                                        automation.alias,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            automation.description ??
                                                'No description',
                                            style: TextStyle(
                                              color: Colors.blue.withValues(
                                                alpha: 0.7,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            'State: ${automation.state?.toUpperCase() ?? 'UNKNOWN'}',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color:
                                                  isActive
                                                      ? Colors.green
                                                      : Colors.grey,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      trailing: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          IconButton(
                                            icon: const Icon(Icons.play_arrow),
                                            color: Colors.blue,
                                            onPressed: () {
                                              _triggerAutomation(automation);
                                            },
                                          ),
                                          PopupMenuButton<String>(
                                            icon: const Icon(Icons.more_vert),
                                            onSelected: (value) {
                                              if (value == 'toggle') {
                                                _toggleAutomation(
                                                  automation,
                                                  !isActive,
                                                );
                                              }
                                            },
                                            itemBuilder:
                                                (BuildContext context) => [
                                                  PopupMenuItem<String>(
                                                    value: 'toggle',
                                                    child: Row(
                                                      children: [
                                                        Text(
                                                          isActive
                                                              ? 'Disable'
                                                              : 'Enable',
                                                        ),
                                                        const SizedBox(
                                                          width: 8,
                                                        ),
                                                        Switch(
                                                          value: isActive,
                                                          onChanged: (value) {
                                                            Navigator.pop(
                                                              context,
                                                            );
                                                            _toggleAutomation(
                                                              automation,
                                                              value,
                                                            );
                                                          },
                                                          activeColor:
                                                              Colors.green,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                          ),
                                        ],
                                      ),
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) =>
                                                    AutomationDetailPage(
                                                      automation: automation,
                                                    ),
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
                    ),
                  ],
                ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateAutomationPage(),
            ),
          );
          if (result == true) {
            _loadAutomations();
          }
        },
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}

// [Rest of the code remains the same - CreateAutomationPage, AutomationDetailPage, and MyApp classes]

class CreateAutomationPage extends StatefulWidget {
  const CreateAutomationPage({super.key});

  @override
  State<CreateAutomationPage> createState() => _CreateAutomationPageState();
}

class _CreateAutomationPageState extends State<CreateAutomationPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descController = TextEditingController();

  final List<AutomationTrigger> _triggers = [];
  final List<AutomationCondition> _conditions = [];
  final List<AutomationAction> _actions = [];
  TimeOfDay? _selectedTime;
  String? _selectedTriggerType;
  String? _selectedSceneId;
  List<Scene> _availableScenes = [];
  bool _isSubmitting = false;
  bool _scenesLoaded = false;
  bool _isLoadingScenes = false;
  String? _loadError;
  bool _initialState = true;

  @override
  void initState() {
    super.initState();
    _loadScenes();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descController.dispose();
    super.dispose();
  }

  Future<void> _loadScenes() async {
    setState(() {
      _isLoadingScenes = true;
      _loadError = null;
      _scenesLoaded = false;
    });

    try {
      final scenes = await AutomationApiService.getAllScenes();

      if (mounted) {
        setState(() {
          _availableScenes = scenes;
          _scenesLoaded = true;
          _isLoadingScenes = false;
          _loadError = null;
        });

        if (scenes.isEmpty) {
          setState(() {
            _loadError = 'No scenes found. Create scenes first.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _scenesLoaded = true;
          _isLoadingScenes = false;
          _availableScenes = [];
          _loadError = 'Failed to load scenes: $e';
        });
      }
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (time != null) {
      setState(() => _selectedTime = time);
    }
  }

  void _addTimeTrigger() {
    if (_selectedTime != null) {
      setState(() {
        _triggers.add(
          AutomationTrigger(
            platform: 'time',
            data: {
              'at':
                  '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}',
            },
          ),
        );
        _selectedTime = null;
        _selectedTriggerType = null;
      });
    }
  }

  void _addSunsetTrigger() {
    setState(() {
      _triggers.add(
        AutomationTrigger(
          platform: 'sun',
          data: {'event': 'sunset', 'offset': '-01:00:00'},
        ),
      );
    });
  }

  void _addStateTrigger() {
    setState(() {
      _triggers.add(
        AutomationTrigger(
          platform: 'state',
          data: {'entity_id': 'binary_sensor.motion_sensor', 'to': 'on'},
        ),
      );
    });
  }

  void _addSceneAction() {
    if (_selectedSceneId != null) {
      setState(() {
        _actions.add(AutomationAction(scene: _selectedSceneId!));
        _selectedSceneId = null;
      });
    }
  }

  void _addServiceAction() {
    setState(() {
      _actions.add(
        AutomationAction(
          service: 'light.turn_on',
          target: {'entity_id': 'light.living_room'},
        ),
      );
    });
  }

  void _addNotificationAction() {
    setState(() {
      _actions.add(
        AutomationAction(
          service: 'notify.notify',
          data: {'message': 'Automation triggered'},
        ),
      );
    });
  }

  void _removeTrigger(int index) {
    setState(() {
      _triggers.removeAt(index);
    });
  }

  void _removeAction(int index) {
    setState(() {
      _actions.removeAt(index);
    });
  }

  String _getSceneName(String entityId) {
    try {
      final scene = _availableScenes.firstWhere((s) => s.entityId == entityId);
      return scene.name;
    } catch (e) {
      return entityId.replaceFirst('scene.', '');
    }
  }

  Future<void> _submitAutomation() async {
    if (!_formKey.currentState!.validate()) return;

    if (_triggers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one trigger')),
      );
      return;
    }

    if (_actions.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one action')),
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final automationId =
          'automation_${DateTime.now().millisecondsSinceEpoch}';
      final automation = Automation(
        id: automationId,
        alias: _nameController.text,
        description: _descController.text.isEmpty ? null : _descController.text,
        triggers: _triggers,
        conditions: _conditions.isNotEmpty ? _conditions : null,
        actions: _actions,
        mode: 'single',
        initial_state: _initialState,
        entityId: 'automation.$automationId',
      );

      final success = await AutomationApiService.createAutomation(automation);

      if (success && mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Automation created successfully')),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to create automation')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Automation')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Automation Name',
                  border: OutlineInputBorder(),
                ),
                validator:
                    (value) =>
                        value?.isEmpty ?? true ? 'Name is required' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              SwitchListTile(
                title: const Text('Initial State'),
                subtitle: const Text('Enable/disable automation by default'),
                value: _initialState,
                onChanged: (value) => setState(() => _initialState = value),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16.0),
                child: Text(
                  'Triggers',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
              Wrap(
                spacing: 8,
                children: [
                  ElevatedButton(
                    onPressed: _addSunsetTrigger,
                    child: const Text('Add Sunset Trigger'),
                  ),
                  ElevatedButton(
                    onPressed: _addStateTrigger,
                    child: const Text('Add State Trigger'),
                  ),
                  ElevatedButton(
                    onPressed:
                        () => setState(() => _selectedTriggerType = 'time'),
                    child: const Text('Add Time Trigger'),
                  ),
                ],
              ),
              if (_selectedTriggerType == 'time') ...[
                const SizedBox(height: 16),
                ListTile(
                  title: Text(
                    _selectedTime?.format(context) ?? 'No time selected',
                  ),
                  subtitle: const Text('Tap to select time'),
                  leading: const Icon(Icons.access_time),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () => _selectTime(context),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                const SizedBox(height: 8),
                if (_selectedTime != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _addTimeTrigger,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Time Trigger'),
                    ),
                  ),
              ],
              if (_triggers.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'Added Triggers:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                ...List.generate(_triggers.length, (index) {
                  final trigger = _triggers[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Icon(
                        trigger.platform == 'time'
                            ? Icons.access_time
                            : trigger.platform == 'sun'
                            ? Icons.wb_sunny
                            : Icons.swap_horiz,
                        color: Colors.blue,
                      ),
                      title: Text(
                        trigger.platform == 'time'
                            ? 'At ${trigger.data['at']}'
                            : trigger.platform == 'sun'
                            ? 'Sunset with offset ${trigger.data['offset']}'
                            : '${trigger.data['entity_id']} → ${trigger.data['to']}',
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _removeTrigger(index),
                      ),
                    ),
                  );
                }),
              ],
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16.0),
                child: Text(
                  'Actions',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
              Wrap(
                spacing: 8,
                children: [
                  ElevatedButton(
                    onPressed: _addServiceAction,
                    child: const Text('Add Light Action'),
                  ),
                  ElevatedButton(
                    onPressed: _addNotificationAction,
                    child: const Text('Add Notification'),
                  ),
                ],
              ),
              if (_availableScenes.isNotEmpty) ...[
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedSceneId,
                  hint: const Text('Select a scene to activate'),
                  items:
                      _availableScenes.map((scene) {
                        return DropdownMenuItem<String>(
                          value: scene.entityId,
                          child: Text(scene.name),
                        );
                      }).toList(),
                  onChanged: (sceneId) {
                    setState(() => _selectedSceneId = sceneId);
                  },
                  decoration: const InputDecoration(
                    labelText: 'Scene Action',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 8),
                if (_selectedSceneId != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _addSceneAction,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Scene Action'),
                    ),
                  ),
              ],
              if (_actions.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'Added Actions:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                ...List.generate(_actions.length, (index) {
                  final action = _actions[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Icon(
                        action.scene != null ? Icons.palette : Icons.settings,
                        color: Colors.green,
                      ),
                      title: Text(
                        action.scene != null
                            ? 'Activate: ${_getSceneName(action.scene!)}'
                            : 'Service: ${action.service}',
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _removeAction(index),
                      ),
                    ),
                  );
                }),
              ],
              Padding(
                padding: const EdgeInsets.only(top: 32.0, bottom: 16.0),
                child: SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isSubmitting ? null : _submitAutomation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child:
                        _isSubmitting
                            ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                ),
                                SizedBox(width: 12),
                                Text('Creating...'),
                              ],
                            )
                            : const Text(
                              'Create Automation',
                              style: TextStyle(fontSize: 16),
                            ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class AutomationDetailPage extends StatefulWidget {
  final Automation automation;

  const AutomationDetailPage({super.key, required this.automation});

  @override
  State<AutomationDetailPage> createState() => _AutomationDetailPageState();
}

class _AutomationDetailPageState extends State<AutomationDetailPage> {
  Automation? _detailedAutomation;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAutomationDetails();
  }

  Future<void> _loadAutomationDetails() async {
    setState(() => _isLoading = true);
    try {
      final details = await AutomationApiService.getAutomationDetails(
        widget.automation.id,
      );
      setState(() {
        _detailedAutomation = details ?? widget.automation;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _detailedAutomation = widget.automation;
        _isLoading = false;
      });
    }
  }

  Future<void> _activateScene(String sceneId) async {
    await AutomationApiService.activateScene(sceneId);
  }

  @override
  Widget build(BuildContext context) {
    final automation = _detailedAutomation ?? widget.automation;

    return Scaffold(
      appBar: AppBar(title: Text(automation.alias)),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Colors.blue),
              )
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Description',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              automation.description ??
                                  'No description provided',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Triggers',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            if (automation.triggers.isEmpty)
                              const Text('No triggers configured')
                            else
                              ...automation.triggers.map(
                                (trigger) => Padding(
                                  padding: const EdgeInsets.only(bottom: 4.0),
                                  child: Text(
                                    '${trigger.platform}: ${trigger.data}',
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Actions',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            if (automation.actions.isEmpty)
                              const Text('No actions configured')
                            else
                              ...automation.actions.map(
                                (action) => Padding(
                                  padding: const EdgeInsets.only(bottom: 4.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        action.scene != null
                                            ? 'Scene: ${action.scene}'
                                            : 'Service: ${action.service}',
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                      if (action.scene != null)
                                        ElevatedButton(
                                          onPressed:
                                              () =>
                                                  _activateScene(action.scene!),
                                          child: const Text('Activate Now'),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Settings',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Text('Mode: '),
                                Text(
                                  automation.mode,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Text('Initial State: '),
                                Text(
                                  automation.initial_state ?? true
                                      ? 'Enabled'
                                      : 'Disabled',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        automation.initial_state ?? true
                                            ? Colors.green
                                            : Colors.red,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
}

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Home Assistant Automations',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
        inputDecorationTheme: const InputDecorationTheme(
          border: OutlineInputBorder(),
        ),
      ),
      home: const AutomationListPage(),
    );
  }
}
