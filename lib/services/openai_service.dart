import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;

class OpenAIService {
  static const String _baseUrl = 'https://api.openai.com/v1/chat/completions';
  static const String _apiKey = '********************************************************************************************************************************************************************';

  static Future<Map<String, dynamic>?> convertVoiceToAutomation(
    String userInput,
  ) async {
    try {
      log('Converting voice input to automation: $userInput');

      final prompt = '''
You are a Home Assistant automation expert. Convert the following natural language into a JSON automation configuration.

User input: "$userInput"

Return ONLY a valid JSON object with this exact structure:
{
  "alias": "meaningful automation name",
  "description": "what this automation does",
  "triggers": [
    {
      "platform": "time|state|sun|device|event|mqtt|webhook",
      "at": "HH:MM" (for time triggers),
      "entity_id": "entity.id" (for state triggers),
      "to": "on|off" (for state triggers),
      "event": "sunrise|sunset" (for sun triggers)
    }
  ],
  "conditions": [],
  "actions": [
    {
      "scene": "scene.scene_name" (for scene activation),
      "service": "domain.service_name" (for service calls),
      "target": {"entity_id": "entity.id"},
      "data": {"key": "value"}
    }
  ],
  "mode": "single"
}

Common examples:
- "Turn on scene 1234 at 6:29 PM" → time trigger at "18:29", scene action "scene.1234"
- "Turn on lights when motion detected" → state trigger on motion sensor, light service
- "Good night routine at 10 PM" → time trigger at "22:00", scene action
- "Turn off all lights at sunrise" → sun trigger "sunrise", light.turn_off service

Be smart about:
- Converting 12-hour to 24-hour time format
- Inferring entity types (light.*, switch.*, scene.*, etc.)
- Creating meaningful aliases and descriptions
- Using appropriate trigger platforms

Return ONLY the JSON, no explanations.
''';

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-4',
          'messages': [
            {'role': 'user', 'content': prompt}
          ],
          'temperature': 0.2,
          'max_tokens': 1000,
        }),
      );

      log('OpenAI API response status: ${response.statusCode}');
      log('OpenAI API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'].trim();
        
        // Clean up the response to extract JSON
        String jsonContent = content;
        if (content.startsWith('```json')) {
          jsonContent = content.substring(7);
        }
        if (content.startsWith('```')) {
          jsonContent = content.substring(3);
        }
        if (jsonContent.endsWith('```')) {
          jsonContent = jsonContent.substring(0, jsonContent.length - 3);
        }
        
        try {
          final automationData = jsonDecode(jsonContent.trim());
          log('Successfully parsed automation data: $automationData');
          return automationData;
        } catch (e) {
          log('Error parsing JSON response: $e');
          log('Raw content: $content');
          return null;
        }
      } else {
        log('OpenAI API error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      log('Error calling OpenAI API: $e');
      return null;
    }
  }

  static Future<String?> improveAutomationDescription(
    String currentDescription,
    Map<String, dynamic> automationData,
  ) async {
    try {
      final prompt = '''
Improve this Home Assistant automation description to be more user-friendly and descriptive.

Current description: "$currentDescription"
Automation data: ${jsonEncode(automationData)}

Return a single, clear, and descriptive sentence that explains what this automation does.
Do not include quotes or extra formatting, just the improved description text.
''';

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-4',
          'messages': [
            {'role': 'user', 'content': prompt}
          ],
          'temperature': 0.3,
          'max_tokens': 100,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'].trim();
        return content.replaceAll('"', '').trim();
      }
      return null;
    } catch (e) {
      log('Error improving description: $e');
      return null;
    }
  }
}
