import 'dart:developer';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:permission_handler/permission_handler.dart';

class SpeechService {
  static final SpeechToText _speechToText = SpeechToText();
  static bool _speechEnabled = false;
  static bool _isListening = false;

  static bool get isListening => _isListening;
  static bool get isEnabled => _speechEnabled;

  static Future<bool> initialize() async {
    try {
      // Request microphone permission
      final permissionStatus = await Permission.microphone.request();
      if (permissionStatus != PermissionStatus.granted) {
        log('Microphone permission denied');
        return false;
      }

      // Initialize speech to text
      _speechEnabled = await _speechToText.initialize(
        onError: (error) {
          log('Speech recognition error: ${error.errorMsg}');
          _isListening = false;
        },
        onStatus: (status) {
          log('Speech recognition status: $status');
          _isListening = status == 'listening';
        },
      );

      log('Speech service initialized: $_speechEnabled');
      return _speechEnabled;
    } catch (e) {
      log('Error initializing speech service: $e');
      return false;
    }
  }

  static Future<String?> startListening({
    Duration? timeout,
    Function(String)? onPartialResult,
  }) async {
    if (!_speechEnabled) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    try {
      String? finalResult;
      bool completed = false;

      await _speechToText.listen(
        onResult: (result) {
          log(
            'Speech result: ${result.recognizedWords} (final: ${result.finalResult})',
          );

          if (onPartialResult != null) {
            onPartialResult(result.recognizedWords);
          }

          if (result.finalResult) {
            finalResult = result.recognizedWords;
            completed = true;
          }
        },
        listenFor: timeout ?? const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 3),
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: true,
          listenMode: ListenMode.confirmation,
        ),
        localeId: 'en_US',
      );

      // Wait for completion or timeout
      final startTime = DateTime.now();
      final maxWait = timeout ?? const Duration(seconds: 30);

      while (!completed && DateTime.now().difference(startTime) < maxWait) {
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (!completed) {
        await stopListening();
      }

      return finalResult;
    } catch (e) {
      log('Error during speech recognition: $e');
      _isListening = false;
      return null;
    }
  }

  static Future<void> stopListening() async {
    try {
      await _speechToText.stop();
      _isListening = false;
      log('Speech recognition stopped');
    } catch (e) {
      log('Error stopping speech recognition: $e');
    }
  }

  static Future<void> cancel() async {
    try {
      await _speechToText.cancel();
      _isListening = false;
      log('Speech recognition cancelled');
    } catch (e) {
      log('Error cancelling speech recognition: $e');
    }
  }

  static Future<List<String>> getAvailableLocales() async {
    final locales = await _speechToText.locales();
    return locales.map((locale) => locale.localeId).toList();
  }

  static bool get hasPermission => _speechEnabled;
}
