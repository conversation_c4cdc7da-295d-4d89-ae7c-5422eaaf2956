import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import '../models/matter_device.dart';
import '../config/api_config.dart';

/// Response validation result for chip-tool API calls
class ChipToolResponse {
  final bool success;
  final String? errorMessage;
  final Map<String, dynamic>? data;
  final int? returnCode;
  final String? stdout;
  final String? stderr;

  ChipToolResponse({
    required this.success,
    this.errorMessage,
    this.data,
    this.returnCode,
    this.stdout,
    this.stderr,
  });

  factory ChipToolResponse.fromDioResponse(
    Response response, {
    String? operationType,
  }) {
    try {
      if (response.statusCode != 200) {
        return ChipToolResponse(
          success: false,
          errorMessage:
              'HTTP ${response.statusCode}: ${response.statusMessage}',
        );
      }

      final Map<String, dynamic> data;
      if (response.data is Map<String, dynamic>) {
        data = response.data;
      } else if (response.data is String) {
        data = jsonDecode(response.data);
      } else {
        data = {'data': response.data};
      }

      // Handle both simple and compound response formats
      int? returnCode;
      String stdout = '';
      String stderr = '';

      // Check if this is a compound response (binding operation)
      if (data.containsKey('acl_command') &&
          data.containsKey('binding_command')) {
        // Compound response format for binding operations
        final aclCommand = data['acl_command'] as Map<String, dynamic>? ?? {};
        final bindingCommand =
            data['binding_command'] as Map<String, dynamic>? ?? {};

        final aclReturnCode = aclCommand['returncode'] as int? ?? -1;
        final bindingReturnCode = bindingCommand['returncode'] as int? ?? -1;

        // Both commands must succeed for overall success
        returnCode = (aclReturnCode == 0 && bindingReturnCode == 0) ? 0 : 1;

        // Combine stdout from both commands
        final aclStdout = aclCommand['stdout'] as String? ?? '';
        final bindingStdout = bindingCommand['stdout'] as String? ?? '';
        stdout = '$aclStdout\n$bindingStdout';

        // Combine stderr from both commands
        final aclStderr = aclCommand['stderr'] as String? ?? '';
        final bindingStderr = bindingCommand['stderr'] as String? ?? '';
        stderr = '$aclStderr\n$bindingStderr'.trim();
      } else {
        // Simple response format for single operations
        returnCode = data['returncode'] as int? ?? -1;
        stdout = data['stdout'] as String? ?? '';
        stderr = data['stderr'] as String? ?? '';
      }

      // Success is determined by returncode 0 and specific stdout messages
      final success =
          returnCode == 0 && _containsSuccessMessage(stdout, operationType);

      return ChipToolResponse(
        success: success,
        errorMessage:
            success
                ? null
                : _extractErrorMessage(
                  returnCode,
                  stdout,
                  stderr,
                  operationType,
                ),
        data: data,
        returnCode: returnCode,
        stdout: stdout,
        stderr: stderr,
      );
    } catch (e) {
      return ChipToolResponse(
        success: false,
        errorMessage: 'Failed to parse response: $e',
      );
    }
  }

  static bool _containsSuccessMessage(String stdout, [String? operationType]) {
    // Common success patterns for chip-tool operations
    final successPatterns = [
      'Shutting down',
      'Device commissioning completed with success',
      'Clearing Default storage',
      'Successfully finished',
      'Command completed successfully',
      'Binding table entry added',
      'ACL entry added successfully',
    ];

    // Add operation-specific patterns
    if (operationType != null) {
      switch (operationType.toLowerCase()) {
        case 'commission':
        case 'pair':
          successPatterns.addAll([
            'Device commissioning completed',
            'Commissioning completed successfully',
          ]);
          break;
        case 'bind':
        case 'binding':
          successPatterns.addAll([
            'Binding table entry added',
            'Binding created successfully',
          ]);
          break;
        case 'clear':
        case 'storage':
          successPatterns.addAll([
            'Clearing Default storage',
            'Storage cleared successfully',
          ]);
          break;
        case 'toggle':
        case 'test':
          successPatterns.addAll(['Command completed', 'Toggle successful']);
          break;
      }
    }

    return successPatterns.any((pattern) => stdout.contains(pattern));
  }

  static String _extractErrorMessage(
    int returnCode,
    String stdout,
    String stderr, [
    String? operationType,
  ]) {
    final operation = operationType != null ? '$operationType ' : '';

    if (stderr.isNotEmpty) {
      return '${operation}error (code $returnCode): $stderr';
    }
    if (stdout.isNotEmpty && !_containsSuccessMessage(stdout, operationType)) {
      return '${operation}failed (code $returnCode): $stdout';
    }
    return '${operation}failed with return code $returnCode';
  }
}

class MatterBindingService {
  // Use centralized API configuration
  static String get _pairUrl => ApiConfig.pairUrl;
  static String get _bindUrl => ApiConfig.bindUrl;
  static String get _toggleUrl => ApiConfig.toggleUrl;
  static String get _commandUrl => ApiConfig.commandUrl;
  
  final List<MatterDevice> _devices = [];
  final List<MatterBinding> _bindings = [];
  final StreamController<List<MatterDevice>> _devicesController = StreamController.broadcast();
  final StreamController<List<MatterBinding>> _bindingsController = StreamController.broadcast();

  // Constructor to initialize default devices
  MatterBindingService() {
    initializeDevices();
  }

  Stream<List<MatterDevice>> get devicesStream => _devicesController.stream;
  Stream<List<MatterBinding>> get bindingsStream => _bindingsController.stream;

  List<MatterDevice> get devices => List.unmodifiable(_devices);
  List<MatterBinding> get bindings => List.unmodifiable(_bindings);

  // Initialize with default Matter devices from Home Assistant
  void initializeDevices() {
    _devices.clear();
    
    // Add default devices based on entity IDs
    _devices.addAll([
      const MatterDevice(
        entityId: 'fan.test_product',
        name: 'Matter Fan',
        deviceType: MatterDeviceTypes.fan,
        endpoints: [MatterEndpoint(endpointId: 1, supportedClusters: [MatterClusters.onOff, MatterClusters.fanControl])],
      ),
      const MatterDevice(
        entityId: 'light.test_product_5',
        name: 'Matter Light',
        deviceType: MatterDeviceTypes.light,
        endpoints: [MatterEndpoint(endpointId: 1, supportedClusters: [MatterClusters.onOff, MatterClusters.levelControl])],
      ),
      const MatterDevice(
        entityId: 'switch.test_product_switch_1',
        name: 'Matter Switch 1',
        deviceType: MatterDeviceTypes.switch_,
        endpoints: [MatterEndpoint(endpointId: 1, supportedClusters: [MatterClusters.onOff])],
      ),
      const MatterDevice(
        entityId: 'switch.test_product_switch_2',
        name: 'Matter Switch 2',
        deviceType: MatterDeviceTypes.switch_,
        endpoints: [MatterEndpoint(endpointId: 1, supportedClusters: [MatterClusters.onOff])],
      ),
    ]);
    
    _devicesController.add(_devices);
    log('Initialized ${_devices.length} Matter devices');
  }

  // Commission device to CHIPTool using share code
  Future<bool> commissionDeviceToChipTool(String deviceEntityId, String shareCode, int nodeId) async {
    try {
      log('Commissioning device $deviceEntityId to CHIPTool with node ID $nodeId');
      log('Using URL: $_pairUrl');

      // Following your exact Dio format
      var headers = ApiConfig.headers;
      var data = jsonEncode({'node_id': nodeId, 'passcode': shareCode});
      log('Sending payload: $data');

      var dio = ApiConfig.getDio();
      var response = await dio.request(
        _pairUrl,
        options: Options(method: 'POST', headers: headers),
        data: data,
      );

      log('Response status: ${response.statusCode}');
      log('Response data: ${jsonEncode(response.data)}');

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        response,
        operationType: 'commission',
      );

      if (chipToolResponse.success) {
        // Update device with node ID and commission status
        final deviceIndex = _devices.indexWhere(
          (d) => d.entityId == deviceEntityId,
        );
        if (deviceIndex != -1) {
          _devices[deviceIndex] = _devices[deviceIndex].copyWith(
            nodeId: nodeId,
            shareCode: shareCode,
            isCommissionedToChipTool: true,
          );
          _devicesController.add(_devices);
          log('Successfully commissioned device $deviceEntityId to CHIPTool');
        }
      } else {
        log('CHIPTool pairing failed: ${chipToolResponse.errorMessage}');
        if (chipToolResponse.stdout?.isNotEmpty == true) {
          log('Stdout: ${chipToolResponse.stdout}');
        }
        if (chipToolResponse.stderr?.isNotEmpty == true) {
          log('Stderr: ${chipToolResponse.stderr}');
        }
      }

      return chipToolResponse.success;
    } catch (e) {
      log('Error commissioning device to CHIPTool: $e');
      return false;
    }
  }

  // Create binding between two Matter devices
  Future<bool> createBinding(
    MatterDevice controllerDevice,
    MatterDevice controlledDevice,
    int controllerEndpoint,
    int controlledEndpoint,
    int clusterId,
  ) async {
    try {
      if (controllerDevice.nodeId == null || controlledDevice.nodeId == null) {
        throw Exception('Both devices must be commissioned to CHIPTool first');
      }

      log('Creating binding: ${controllerDevice.name} -> ${controlledDevice.name}');
      log('Using URL: $_bindUrl');

      // Following your exact Dio format
      var headers = ApiConfig.headers;
      var data = jsonEncode({
        'switch_node': controllerDevice.nodeId!,
        'switch_endpoint': controllerEndpoint,
        'light_node': controlledDevice.nodeId!,
        'light_endpoint': controlledEndpoint,
      });
      log('Sending binding payload: $data');

      var dio = ApiConfig.getDio();
      var response = await dio.request(
        _bindUrl,
        options: Options(method: 'POST', headers: headers),
        data: data,
      );

      log('Binding response status: ${response.statusCode}');
      log('Binding response data: ${jsonEncode(response.data)}');

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        response,
        operationType: 'binding',
      );

      if (chipToolResponse.success) {
        // For binding endpoint, we need to check nested command results
        final result = chipToolResponse.data!;
        final aclCommand = result['acl_command'] as Map<String, dynamic>?;
        final bindingCommand =
            result['binding_command'] as Map<String, dynamic>?;

        // Validate both ACL and binding commands
        final aclSuccess = _validateNestedCommand(aclCommand, 'ACL');
        final bindingSuccess = _validateNestedCommand(
          bindingCommand,
          'Binding',
        );

        if (aclSuccess && bindingSuccess) {
          // Add binding to our list
          final binding = MatterBinding(
            id:
                '${controllerDevice.entityId}_${controlledDevice.entityId}_${DateTime.now().millisecondsSinceEpoch}',
            controllerDevice: controllerDevice,
            controlledDevice: controlledDevice,
            controllerEndpoint: controllerEndpoint,
            controlledEndpoint: controlledEndpoint,
            clusterId: clusterId,
            createdAt: DateTime.now(),
          );

          _bindings.add(binding);
          _bindingsController.add(_bindings);

          log('Successfully created binding: ${binding.id}');
          return true;
        } else {
          log(
            'Binding creation failed. ACL success: $aclSuccess, Binding success: $bindingSuccess',
          );
          if (aclCommand != null) {
            log(
              'ACL command details: returncode=${aclCommand['returncode']}, stdout=${aclCommand['stdout']}',
            );
          }
          if (bindingCommand != null) {
            log(
              'Binding command details: returncode=${bindingCommand['returncode']}, stdout=${bindingCommand['stdout']}',
            );
          }
          return false;
        }
      } else {
        log('Binding creation failed: ${chipToolResponse.errorMessage}');
        return false;
      }
    } catch (e) {
      log('Error creating binding: $e');
      return false;
    }
  }

  // Test device toggle functionality
  Future<bool> testDeviceToggle(int nodeId, int endpointId) async {
    try {
      log('Testing device toggle for node $nodeId endpoint $endpointId');
      log('Using URL: $_toggleUrl');

      // Following your exact Dio format
      var headers = ApiConfig.headers;
      var data = jsonEncode({'node_id': nodeId, 'endpoint_id': endpointId});
      log('Sending payload: $data');

      var dio = ApiConfig.getDio();
      var response = await dio.request(
        _toggleUrl,
        options: Options(method: 'POST', headers: headers),
        data: data,
      );

      log('Toggle response status: ${response.statusCode}');
      log('Toggle response data: ${jsonEncode(response.data)}');

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        response,
        operationType: 'toggle',
      );

      if (chipToolResponse.success) {
        log('Device toggle test successful');
      } else {
        log('Device toggle test failed: ${chipToolResponse.errorMessage}');
      }

      return chipToolResponse.success;
    } catch (e) {
      log('Error testing device toggle: $e');
      return false;
    }
  }

  // Clear chip tool storage
  Future<bool> clearChipToolStorage() async {
    try {
      log('Clearing chip tool storage');

      // Following your exact Dio format
      var headers = ApiConfig.headers;
      var data = jsonEncode({
        'args': ['storage', 'clear-all'],
      });

      var dio = ApiConfig.getDio();
      var response = await dio.request(
        _commandUrl,
        options: Options(method: 'POST', headers: headers),
        data: data,
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        response,
        operationType: 'clear',
      );

      if (chipToolResponse.success) {
        log('Chip tool storage cleared successfully');
        // Clear local device and binding lists since storage is cleared
        _devices.clear();
        _bindings.clear();
        initializeDevices(); // Reinitialize with default devices
        _bindingsController.add(_bindings);
      } else {
        log(
          'Failed to clear chip tool storage: ${chipToolResponse.errorMessage}',
        );
      }

      return chipToolResponse.success;
    } catch (e) {
      log('Error clearing chip tool storage: $e');
      return false;
    }
  }

  // Helper method to validate nested command responses (for binding endpoint)
  bool _validateNestedCommand(
    Map<String, dynamic>? command,
    String commandType,
  ) {
    if (command == null) {
      log('$commandType command is null');
      return false;
    }

    final returnCode = command['returncode'] as int? ?? -1;
    final stdout = command['stdout'] as String? ?? '';
    final stderr = command['stderr'] as String? ?? '';

    final success = returnCode == 0 && stdout.contains('Shutting down');

    if (!success) {
      log('$commandType command failed: returncode=$returnCode');
      if (stdout.isNotEmpty) log('$commandType stdout: $stdout');
      if (stderr.isNotEmpty) log('$commandType stderr: $stderr');
    }

    return success;
  }

  // Remove binding
  Future<bool> removeBinding(String bindingId) async {
    try {
      final bindingIndex = _bindings.indexWhere((b) => b.id == bindingId);
      if (bindingIndex == -1) {
        return false;
      }

      // For now, just remove from local list
      // In a full implementation, you would need to call the chip tool to clear the binding
      _bindings.removeAt(bindingIndex);
      _bindingsController.add(_bindings);
      log('Successfully removed binding: $bindingId');

      return true;
    } catch (e) {
      log('Error removing binding: $e');
      return false;
    }
  }

  // Get available controller devices
  List<MatterDevice> getControllerDevices() {
    return _devices.where((device) => 
      device.canBeController && device.isCommissionedToChipTool
    ).toList();
  }

  // Get available controlled devices
  List<MatterDevice> getControlledDevices() {
    return _devices.where((device) => 
      device.canBeControlled && device.isCommissionedToChipTool
    ).toList();
  }

  void dispose() {
    _devicesController.close();
    _bindingsController.close();
  }
}
