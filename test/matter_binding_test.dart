// This is a basic Flutter widget test.
//
// This file contains unit tests for the Matter binding functionality
// and API response handling.

import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:homeautomation/services/matter_binding_service.dart';

void main() {
  group('ChipToolResponse', () {
    test('should parse successful commission response correctly', () {
      final mockResponse = Response(
        data: {
          'returncode': 0,
          'stdout':
              'Device commissioning completed with success\nShutting down',
          'stderr': '',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/pair'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'commission',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.errorMessage, isNull);
      expect(chipToolResponse.returnCode, equals(0));
      expect(chipToolResponse.stdout, contains('Shutting down'));
    });

    test('should parse failed commission response correctly', () {
      final mockResponse = Response(
        data: {
          'returncode': 1,
          'stdout': 'Failed to commission device',
          'stderr': 'Connection timeout',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/pair'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'commission',
      );

      expect(chipToolResponse.success, isFalse);
      expect(chipToolResponse.errorMessage, isNotNull);
      expect(chipToolResponse.returnCode, equals(1));
      expect(chipToolResponse.errorMessage, contains('commission'));
    });

    test('should handle HTTP error responses', () {
      final mockResponse = Response(
        data: 'Not Found',
        statusCode: 404,
        statusMessage: 'Not Found',
        requestOptions: RequestOptions(path: '/pair'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(mockResponse);

      expect(chipToolResponse.success, isFalse);
      expect(chipToolResponse.errorMessage, contains('HTTP 404'));
    });

    test('should recognize clear storage success message', () {
      final mockResponse = Response(
        data: {
          'returncode': 0,
          'stdout': 'Clearing Default storage\nShutting down',
          'stderr': '',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/command'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'clear',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.stdout, contains('Clearing Default storage'));
    });

    test('should recognize binding success patterns', () {
      final mockResponse = Response(
        data: {
          'returncode': 0,
          'stdout': 'Binding table entry added\nShutting down',
          'stderr': '',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/bind'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'binding',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.errorMessage, isNull);
    });

    test('should detect commission failure with returncode 1', () {
      final mockResponse = Response(
        data: {
          'returncode': 1,
          'stdout': 'Failed to establish secure session',
          'stderr': 'CHIP Error 0x00000003: Incorrect state',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/pair'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'commission',
      );

      expect(chipToolResponse.success, isFalse);
      expect(chipToolResponse.returnCode, equals(1));
      expect(chipToolResponse.errorMessage, contains('commission'));
      expect(chipToolResponse.stderr, contains('CHIP Error'));
    });

    test('should handle compound binding response format', () {
      final mockResponse = Response(
        data: {
          'acl_command': {
            'returncode': 0,
            'stdout': 'WriteClient moving to [AwaitingDe]\nShutting down',
            'stderr': '',
          },
          'binding_command': {
            'returncode': 0,
            'stdout': 'WriteClient moving to [AwaitingDe]\nShutting down',
            'stderr': '',
          },
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/bind'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'binding',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.returnCode, equals(0));
      expect(chipToolResponse.stdout, contains('Shutting down'));
      expect(chipToolResponse.errorMessage, isNull);
    });

    test('should detect compound binding failure when one command fails', () {
      final mockResponse = Response(
        data: {
          'acl_command': {
            'returncode': 0,
            'stdout': 'WriteClient moving to [AwaitingDe]\nShutting down',
            'stderr': '',
          },
          'binding_command': {
            'returncode': 1,
            'stdout': 'Failed to create binding',
            'stderr': 'CHIP Error: Binding failed',
          },
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/bind'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'binding',
      );

      expect(chipToolResponse.success, isFalse);
      expect(chipToolResponse.returnCode, equals(1));
      expect(chipToolResponse.stderr, contains('CHIP Error: Binding failed'));
    });

    test('should handle real binding success response from user file', () {
      // This is the actual response format from the user's response.txt file
      final mockResponse = Response(
        data: {
          'acl_command': {
            'command':
                '/app/connected_home_ip/out/chip-tool-linux/chip-tool accesscontrol write acl',
            'returncode': 0,
            'stderr': '',
            'stdout':
                '[0;34m[1750764174.733] [155:155] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_tool_kvs[0m\n[0;34m[1750764176.024] [155:155] [CTL] Shutting down the commissioner[0m\n[0;32m[1750764176.034] [155:155] [DL] System Layer shutdown[0m\n',
          },
          'binding_command': {
            'command':
                '/app/connected_home_ip/out/chip-tool-linux/chip-tool binding write binding',
            'returncode': 0,
            'stderr': '',
            'stdout':
                '[0;34m[1750764176.076] [158:158] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_tool_kvs[0m\n[0;34m[1750764177.303] [158:158] [CTL] Shutting down the commissioner[0m\n[0;32m[1750764177.313] [158:158] [DL] System Layer shutdown[0m\n',
          },
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/bind'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'binding',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.returnCode, equals(0));
      expect(chipToolResponse.stdout, contains('Shutting down'));
      expect(chipToolResponse.errorMessage, isNull);
    });
  });
}
